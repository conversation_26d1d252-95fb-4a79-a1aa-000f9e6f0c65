import React, { useState } from 'react';
import { Sidebar } from './components/Sidebar';
import { Dashboard } from './components/Dashboard';
import { ShortCodeManagement } from './components/ShortCodeManagement';
import { AccessRecords } from './components/AccessRecords';
import { UserManagement } from './components/UserManagement';
import { DomainManagement } from './components/DomainManagement';
import { LinkPolling } from './components/LinkPolling';
import { OperationLogs } from './components/OperationLogs';
import { Login } from './components/Login';
import { Toaster } from './components/ui/sonner';

interface User {
  id: string;
  name: string;
  email: string;
  role: 'super-admin' | 'boss' | 'manager';
}

type PageType = 'dashboard' | 'short-codes' | 'access-records' | 'user-management' | 'domain-management' | 'link-polling' | 'operation-logs';

export default function App() {
  const [currentView, setCurrentView] = useState<PageType>('dashboard');
  const [user, setUser] = useState<User | null>(null);
  const [selectedShortCodeId, setSelectedShortCodeId] = useState<string | null>(null);

  // 模拟用户登录
  const handleLogin = (userData: { email: string; password: string }) => {
    // 模拟不同角色的用户
    let mockUser: User;
    if (userData.email === '<EMAIL>') {
      mockUser = {
        id: '1',
        name: '超级管理员',
        email: '<EMAIL>',
        role: 'super-admin'
      };
    } else if (userData.email === '<EMAIL>') {
      mockUser = {
        id: '2',
        name: '张总',
        email: '<EMAIL>',
        role: 'boss'
      };
    } else {
      mockUser = {
        id: '3',
        name: '李经理',
        email: '<EMAIL>',
        role: 'manager'
      };
    }
    setUser(mockUser);
  };

  const handleLogout = () => {
    setUser(null);
    setCurrentView('dashboard');
  };

  // 页面导航处理
  const handlePageChange = (page: string) => {
    setCurrentView(page as PageType);
  };

  // 处理从短码管理跳转到链接轮询
  const handleNavigateToPolling = (shortCodeId: string) => {
    setSelectedShortCodeId(shortCodeId);
    setCurrentView('link-polling');
  };

  // 处理从短码管理跳转到访问记录
  const handleNavigateToAccess = (shortCodeId: string) => {
    setSelectedShortCodeId(shortCodeId);
    setCurrentView('access-records');
  };

  // 如果未登录，显示登录页面
  if (!user) {
    return (
      <>
        <Login onLogin={handleLogin} />
        <Toaster />
      </>
    );
  }

  // 根据当前视图渲染对应组件
  const renderContent = () => {
    switch (currentView) {
      case 'dashboard':
        return <Dashboard user={user} onNavigate={handlePageChange} />;
      case 'short-codes':
        return <ShortCodeManagement 
          user={user} 
          onNavigateToAccess={handleNavigateToAccess}
          onNavigateToPolling={handleNavigateToPolling}
        />;
      case 'access-records':
        return <AccessRecords user={user} selectedShortCodeId={selectedShortCodeId} />;
      case 'user-management':
        return <UserManagement user={user} />;
      case 'domain-management':
        return <DomainManagement user={user} />;
      case 'link-polling':
        return <LinkPolling user={user} selectedShortCodeId={selectedShortCodeId} />;
      case 'operation-logs':
        return <OperationLogs user={user} />;
      default:
        return <Dashboard user={user} onNavigate={handlePageChange} />;
    }
  };

  return (
    <>
      <div className="flex h-screen bg-slate-50/30">
        {/* 侧边栏 */}
        <Sidebar 
          user={user}
          currentPage={currentView}
          onPageChange={handlePageChange}
          onLogout={handleLogout}
        />
        
        {/* 主内容区域 */}
        <main className="flex-1 overflow-auto transition-all duration-300">
          {renderContent()}
        </main>
      </div>
      <Toaster />
    </>
  );
}