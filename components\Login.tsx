import React, { useState } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Eye, EyeOff, Shield } from 'lucide-react';

interface User {
  id: string;
  name: string;
  email: string;
  role: 'super-admin' | 'boss' | 'manager';
  avatar?: string;
}

interface LoginProps {
  onLogin: (user: User) => void;
}

export function Login({ onLogin }: LoginProps) {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // 模拟用户数据
  const mockUsers = {
    'admin': {
      id: '1',
      name: '超级管理员',
      email: '<EMAIL>',
      role: 'super-admin' as const,
      password: 'admin123'
    },
    'boss': {
      id: '2', 
      name: '张老板',
      email: '<EMAIL>',
      role: 'boss' as const,
      password: 'boss123'
    },
    'manager': {
      id: '3',
      name: '李经理',
      email: '<EMAIL>', 
      role: 'manager' as const,
      password: 'manager123'
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // 模拟登录验证
    setTimeout(() => {
      const user = mockUsers[username as keyof typeof mockUsers];
      if (user && user.password === password) {
        const { password: _, ...userWithoutPassword } = user;
        onLogin(userWithoutPassword);
      } else {
        alert('用户名或密码错误');
      }
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-4">
      <div className="w-full max-w-md">
        <Card className="border-0 shadow-2xl bg-white/95 backdrop-blur-sm">
          <CardHeader className="text-center pb-2">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                <Shield className="w-8 h-8 text-white" />
              </div>
            </div>
            <CardTitle className="text-3xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              活码管理系统
            </CardTitle>
            <CardDescription className="text-base mt-2">
              请使用您的账号登录系统
            </CardDescription>
          </CardHeader>
          
          <CardContent className="pt-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="username" className="text-sm font-medium">用户名</Label>
                <Input
                  id="username"
                  type="text"
                  placeholder="请输入用户名"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className="h-11 bg-slate-50/50 border-slate-200 focus:bg-white transition-all duration-200"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password" className="text-sm font-medium">密码</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="请输入密码"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="h-11 bg-slate-50/50 border-slate-200 focus:bg-white transition-all duration-200 pr-12"
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1 h-9 w-9 hover:bg-slate-100 transition-colors"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-slate-500" />
                    ) : (
                      <Eye className="h-4 w-4 text-slate-500" />
                    )}
                  </Button>
                </div>
              </div>
              
              <Button 
                type="submit" 
                className="w-full h-11 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-200" 
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    登录中...
                  </div>
                ) : (
                  '登录'
                )}
              </Button>
            </form>
            
            <div className="mt-8 p-4 bg-gradient-to-r from-slate-50 to-blue-50 rounded-xl border border-slate-200/50">
              <div className="text-sm font-medium text-slate-700 mb-3 flex items-center">
                <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                测试账号
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between p-2 bg-white/60 rounded-lg">
                  <span className="text-slate-600">超级管理员:</span>
                  <span className="font-mono text-slate-800">admin / admin123</span>
                </div>
                <div className="flex justify-between p-2 bg-white/60 rounded-lg">
                  <span className="text-slate-600">Boss:</span>
                  <span className="font-mono text-slate-800">boss / boss123</span>
                </div>
                <div className="flex justify-between p-2 bg-white/60 rounded-lg">
                  <span className="text-slate-600">经理:</span>
                  <span className="font-mono text-slate-800">manager / manager123</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}