import { OperationLog, AccessRecord, SystemUser, ShortCode, LinkBatch, LinkData } from '../types';

// Role labels and colors
export const ROLE_LABELS = {
  'super-admin': '超级管理员',
  'boss': 'Boss',
  'manager': '经理'
} as const;

export const ROLE_COLORS = {
  'super-admin': 'bg-red-500',
  'boss': 'bg-blue-500', 
  'manager': 'bg-green-500'
} as const;

// Menu items configuration
export const MENU_ITEMS = [
  {
    id: 'dashboard' as const,
    label: '工作台',
    roles: ['super-admin', 'boss', 'manager']
  },
  {
    id: 'short-codes' as const,
    label: '短码管理',
    roles: ['super-admin', 'boss', 'manager']
  },
  {
    id: 'access-records' as const,
    label: '访问记录',
    roles: ['super-admin', 'boss', 'manager']
  },
  {
    id: 'user-management' as const,
    label: '用户管理',
    roles: ['super-admin', 'boss']
  },
  {
    id: 'domain-management' as const,
    label: '域名管理',
    roles: ['super-admin']
  },
  {
    id: 'link-polling' as const,
    label: '链接轮询',
    roles: ['super-admin', 'boss', 'manager']
  },
  {
    id: 'operation-logs' as const,
    label: '操作日志',
    roles: ['super-admin', 'boss']
  }
];

// Priority count options
export const PRIORITY_COUNT_OPTIONS = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(num => ({
  value: num.toString(),
  label: `${num}次`
}));

// Filter options
export const TIME_FILTER_OPTIONS = [
  { value: 'all', label: '全部时间' },
  { value: 'today', label: '今天' },
  { value: 'week', label: '最近7天' },
  { value: 'month', label: '最近30天' }
];

export const DEVICE_FILTER_OPTIONS = [
  { value: 'all', label: '全部设备' },
  { value: 'mobile', label: '移动设备' },
  { value: 'desktop', label: '桌面设备' },
  { value: 'tablet', label: '平板设备' }
];

export const ROLE_FILTER_OPTIONS = [
  { value: 'all', label: '全部角色' },
  { value: 'boss', label: 'Boss' },
  { value: 'manager', label: '经理' }
];

// Mock data
export const MOCK_OPERATION_LOGS: OperationLog[] = [
  {
    id: '1',
    userId: 'current-user',
    userName: '当前用户',
    userRole: 'manager',
    action: '创建活码',
    targetType: 'short_code',
    targetName: 'WS客服主链接',
    details: '创建了新的WhatsApp类型活码',
    ipAddress: '*************',
    createdAt: '2024-01-15 14:30:25',
    status: 'success'
  },
  {
    id: '2',
    userId: 'user-2',
    userName: '张总',
    userRole: 'boss',
    action: '删除用户',
    targetType: 'user',
    targetName: '离职员工',
    details: '删除了已离职员工的账户',
    ipAddress: '*************',
    createdAt: '2024-01-15 13:45:10',
    status: 'success'
  },
  {
    id: '3',
    userId: 'current-user',
    userName: '当前用户',
    userRole: 'manager',
    action: '批量添加',
    targetType: 'link_data',
    targetName: '新年推广批次',
    details: '为活码添加了15个WhatsApp账号',
    ipAddress: '*************',
    createdAt: '2024-01-15 12:20:15',
    status: 'success'
  },
  {
    id: '4',
    userId: 'user-3',
    userName: '李经理',
    userRole: 'manager',
    action: '编辑活码',
    targetType: 'short_code',
    targetName: 'TG推广渠道A',
    details: '修改了活码标题和描述',
    ipAddress: '*************',
    createdAt: '2024-01-15 11:15:30',
    status: 'success'
  },
  {
    id: '5',
    userId: 'current-user',
    userName: '当前用户',
    userRole: 'manager',
    action: '重置密码',
    targetType: 'user',
    targetName: '王经理',
    details: '重置了用户密码',
    ipAddress: '*************',
    createdAt: '2024-01-15 10:05:45',
    status: 'success'
  },
  {
    id: '6',
    userId: 'system',
    userName: '系统',
    userRole: 'super-admin',
    action: '域名过期',
    targetType: 'domain',
    targetName: 'backup.ly',
    details: '域名即将在3天内过期',
    ipAddress: 'system',
    createdAt: '2024-01-15 09:00:00',
    status: 'warning'
  },
  {
    id: '7',
    userId: 'user-3',
    userName: '李经理',
    userRole: 'manager',
    action: '登录失败',
    targetType: 'auth',
    targetName: '登录系统',
    details: '密码错误，登录失败',
    ipAddress: '*************',
    createdAt: '2024-01-15 08:30:12',
    status: 'failed'
  }
];

// URL generation helpers
export const TARGET_URLS = {
  'WhatsApp': 'https://api.whatsapp.com/send/?phone=',
  'Telegram': 'https://t.me/',
  '网址': 'https://'
} as const;

// Random code generation
export const generateRandomCode = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// Device type labels
export const DEVICE_TYPE_LABELS = {
  'mobile': '移动端',
  'desktop': '桌面端',
  'tablet': '平板端'
} as const;

// Action type labels
export const ACTION_TYPE_LABELS = {
  'create': '新建',
  'visit': '访问',
  'edit': '编辑',
  'login': '登录',
  'warning': '警告'
} as const;