# 企业级活码管理系统权限表格

## 角色定义

| 角色 | 说明 | 级别 |
|------|------|------|
| **超级管理员** | 系统最高权限，可管理所有功能和数据 | 1级 |
| **Boss** | 可管理下属用户，查看全部业务数据 | 2级 |
| **经理** | 仅能管理自己的活码和相关数据 | 3级 |

## 功能权限矩阵

| 功能模块 | 超级管理员 | Boss | 经理 |
|---------|-----------|------|------|
| **工作台** | ✅ | ✅ | ✅ |
| 查看全部统计 | ✅ | ✅ | ❌ |
| 查看个人统计 | ✅ | ✅ | ✅ |
| 查看创建者信息 | ✅ | ✅ | ❌ |
| **短码管理** | ✅ | ✅ | ✅ |
| 创建活码 | ✅ | ✅ | ✅ |
| 查看所有活码 | ✅ | ✅ | ❌ |
| 查看个人活码 | ✅ | ✅ | ✅ |
| 编辑他人活码 | ✅ | ✅ | ❌ |
| 删除他人活码 | ✅ | ✅ | ❌ |
| **链接轮询** | ✅ | ✅ | ✅ |
| 管理所有配置 | ✅ | ✅ | ❌ |
| 管理个人配置 | ✅ | ✅ | ✅ |
| **访问记录** | ✅ | ✅ | ✅ |
| 查看所有记录 | ✅ | ✅ | ❌ |
| 查看个人记录 | ✅ | ✅ | ✅ |
| 导出数据 | ✅ | ✅ | ✅ |
| **用户管理** | ✅ | ✅ | ❌ |
| 创建Boss账户 | ✅ | ❌ | ❌ |
| 创建经理账户 | ✅ | ✅ | ❌ |
| 编辑用户信息 | ✅ | ✅ | ❌ |
| 删除用户 | ✅ | ✅ | ❌ |
| 重置密码 | ✅ | ✅ | ❌ |
| **域名管理** | ✅ | ❌ | ❌ |
| 添加域名 | ✅ | ❌ | ❌ |
| 编辑域名 | ✅ | ❌ | ❌ |
| 删除域名 | ✅ | ❌ | ❌ |
| **操作日志** | ✅ | ✅ | ❌ |
| 查看所有日志 | ✅ | ❌ | ❌ |
| 查看相关日志 | ✅ | ✅ | ❌ |
| 导出日志 | ✅ | ✅ | ❌ |

## 数据可见性规则

### 超级管理员
- 可查看和管理系统所有数据
- 可创建和管理所有角色用户
- 拥有所有模块的完整权限

### Boss
- 可查看所有业务数据（活码、访问记录等）
- 可管理下属经理账户
- 无法访问域名管理
- 无法查看超级管理员的操作日志

### 经理
- 仅可查看和管理自己创建的数据
- 无法查看他人的活码和数据
- 无法访问用户管理、域名管理、操作日志
- 在统计数据中只显示个人相关数据

## 特殊权限说明

1. **热门短码创建者显示**：仅超级管理员和Boss可见
2. **访问记录跳转**：点击访问量可跳转到详细记录
3. **轮询数据跳转**：点击轮询数据可跳转到配置页面
4. **域名地址不可修改**：创建后仅可修改有效期和备注
5. **批次优先级自动转换**：完成后自动变为常规轮询

## 权限验证流程

1. **登录验证** → 获取用户角色
2. **路由权限** → 检查模块访问权限
3. **数据过滤** → 按角色过滤可见数据
4. **操作权限** → 实时验证操作权限
5. **审计记录** → 记录所有操作日志

## 注意事项

- 所有权限变更会记录在操作日志中
- 数据删除操作需要二次确认
- 关联数据删除会显示影响范围警告
- 分页功能在所有列表页面都可用
- 搜索和过滤遵循权限范围限制