# 数据库设计文档

## 概述

本目录包含企业级活码管理系统的完整数据库设计文档，包括表结构定义、关系图和部署说明。

## 文件说明

- `schema.sql` - 完整的数据库表结构和索引定义
- `er_diagram.md` - 实体关系图和关系说明
- `README.md` - 本文档，包含部署和维护说明

## 数据库架构

### 技术选型
- **数据库引擎**: MySQL 8.0+
- **存储引擎**: InnoDB
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci

### 核心表结构
1. **users** - 用户账户管理
2. **domains** - 域名配置管理
3. **short_codes** - 短码核心数据
4. **link_batches** - 链接批次管理
5. **link_data** - 轮询链接数据
6. **access_records** - 访问记录统计
7. **operation_logs** - 操作审计日志

## 部署指南

### 1. 环境要求
```bash
# MySQL版本要求
MySQL 8.0 或更高版本

# 必要权限
CREATE, ALTER, DROP, INSERT, UPDATE, DELETE, SELECT
```

### 2. 数据库创建
```sql
-- 创建数据库
CREATE DATABASE shortcode_management 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE shortcode_management;

-- 执行schema.sql创建表结构
SOURCE schema.sql;
```

### 3. 初始化数据
```sql
-- 创建超级管理员账户
INSERT INTO users (username, email, password_hash, role, status) 
VALUES ('admin', '<EMAIL>', '$2y$10$hash_value_here', 'super-admin', 'active');

-- 添加默认域名
INSERT INTO domains (domain, expiry_date, status, remark) 
VALUES ('short.ly', '2025-12-31', 'active', '默认短链接域名');
```

## 数据维护

### 1. 定期清理
```sql
-- 清理90天前的访问记录
DELETE FROM access_records 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);

-- 清理180天前的操作日志
DELETE FROM operation_logs 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 180 DAY);
```

### 2. 性能监控
```sql
-- 检查表大小
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'shortcode_management'
ORDER BY (data_length + index_length) DESC;

-- 检查索引使用情况
SHOW INDEX FROM short_codes;
```

### 3. 备份策略
```bash
# 每日备份
mysqldump --routines --triggers --single-transaction \
    shortcode_management > backup_$(date +%Y%m%d).sql

# 增量备份（启用binlog）
mysqldump --single-transaction --flush-logs --master-data=2 \
    shortcode_management > incremental_backup.sql
```

## 安全配置

### 1. 用户权限
```sql
-- 创建应用专用用户
CREATE USER 'shortcode_app'@'localhost' IDENTIFIED BY 'strong_password';

-- 授予必要权限
GRANT SELECT, INSERT, UPDATE, DELETE ON shortcode_management.* TO 'shortcode_app'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;
```

### 2. 数据加密
- 密码使用 bcrypt 加密存储
- 敏感操作记录在审计日志中
- 定期更新用户密码

### 3. 访问控制
- 限制数据库连接来源
- 启用慢查询日志监控
- 定期审查用户权限

## 扩展考虑

### 1. 读写分离
```sql
-- 主库配置（写操作）
-- 从库配置（读操作）
```

### 2. 分库分表
- `access_records` 按月分表
- `operation_logs` 按年分库
- 热数据和冷数据分离

### 3. 缓存策略
- Redis 缓存热点短码数据
- 短码解析结果缓存
- 统计数据定时更新

## 故障处理

### 1. 常见问题
```sql
-- 检查锁表情况
SHOW PROCESSLIST;

-- 检查死锁
SHOW ENGINE INNODB STATUS;

-- 重建索引
OPTIMIZE TABLE short_codes;
```

### 2. 数据恢复
```bash
# 从备份恢复
mysql shortcode_management < backup_20240115.sql

# 增量恢复
mysqlbinlog binlog.000001 | mysql shortcode_management
```

## 版本历史

### v1.0.0 (2024-01)
- 初始数据库架构设计
- 完成核心表结构
- 建立外键关系和索引

### 后续规划
- 添加数据归档策略
- 实现读写分离
- 集成监控告警系统