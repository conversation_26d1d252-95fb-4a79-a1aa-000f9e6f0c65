import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { Checkbox } from './ui/checkbox';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from './ui/alert-dialog';
import { 
  Search, 
  Trash2, 
  RotateCcw,
  MessageCircle,
  Send,
  Globe,
  Upload,
  Download,
  Settings,
  Target,
  Clock,
  TrendingUp,
  Edit,
  Link as LinkIcon,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react';
import { toast } from 'sonner@2.0.3';

interface User {
  id: string;
  name: string;
  role: 'super-admin' | 'boss' | 'manager';
}

interface ShortCode {
  id: string;
  title: string;
  shortCode: string;
  type: 'WhatsApp' | 'Telegram' | '网址';
}

interface LinkBatch {
  id: string;
  name: string;
  createdAt: string;
  priority: number;
  priorityCount: number; // 优先轮询次数（1-10）
  usedCount: number; // 已使用次数
  status: 'priority' | 'merged' | 'normal';
  linkType: 'WhatsApp' | 'Telegram' | '网址';
  shortCodeId: string;
}

interface LinkData {
  id: string;
  type: 'WhatsApp' | 'Telegram' | '网址';
  content: string;
  addedAt: string;
  batchId: string;
  batchName: string;
  usageCount: number; // 使用次数
  status: 'active' | 'disabled';
  priority: number; // 批次优先级
  shortCodeId: string; // 关联的活码ID
}

interface LinkPollingProps {
  user: User;
  selectedShortCodeId?: string | null;
}

export function LinkPolling({ user, selectedShortCodeId }: LinkPollingProps) {
  const [selectedShortCode, setSelectedShortCode] = useState<string>('');
  const [selectedType, setSelectedType] = useState<'WhatsApp' | 'Telegram' | '网址'>('WhatsApp');
  const [searchTerm, setSearchTerm] = useState('');
  const [isBatchDialogOpen, setIsBatchDialogOpen] = useState(false);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [batchContent, setBatchContent] = useState('');
  const [batchName, setBatchName] = useState('');
  const [batchPriorityCount, setBatchPriorityCount] = useState('5');
  const [deleteInput, setDeleteInput] = useState('');
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // 如果传入了selectedShortCodeId，则预设选中
  useEffect(() => {
    if (selectedShortCodeId) {
      setSelectedShortCode(selectedShortCodeId);
      const shortCode = shortCodes.find(sc => sc.id === selectedShortCodeId);
      if (shortCode) {
        setSelectedType(shortCode.type);
        toast.success(`已自动选择活码：${shortCode.title}`);
      }
    }
  }, [selectedShortCodeId]);

  // 模拟活码数据
  const shortCodes: ShortCode[] = [
    {
      id: '1',
      title: 'WS客服主链接',
      shortCode: 'rJgMftSC',
      type: 'WhatsApp'
    },
    {
      id: '2',
      title: 'TG推广渠道A',
      shortCode: 'kL9mNpQr',
      type: 'Telegram'
    },
    {
      id: '3',
      title: '官网导流',
      shortCode: 'bV3xZcFg',
      type: '网址'
    }
  ];

  // 模拟批次数据
  const linkBatches: LinkBatch[] = [
    {
      id: 'batch-1',
      name: '新年推广批次',
      createdAt: '2024-01-15 10:30:00',
      priority: 3,
      priorityCount: 5,
      usedCount: 5, // 已完成
      status: 'normal', // 已转为常规轮询
      linkType: 'WhatsApp',
      shortCodeId: '1'
    },
    {
      id: 'batch-2',
      name: '客服账号组',
      createdAt: '2024-01-14 15:20:00',
      priority: 2,
      priorityCount: 8,
      usedCount: 8, // 已完成
      status: 'normal', // 已转为常规轮询
      linkType: 'WhatsApp',
      shortCodeId: '1'
    },
    {
      id: 'batch-3',
      name: 'TG频道集合',
      createdAt: '2024-01-13 09:15:00',
      priority: 1,
      priorityCount: 3,
      usedCount: 1,
      status: 'priority',
      linkType: 'Telegram',
      shortCodeId: '2'
    }
  ];

  // 模拟链接数据 - 增加更多数据用于测试分页
  const linkData: LinkData[] = [
    {
      id: '1',
      type: 'WhatsApp',
      content: '254751076496',
      addedAt: '2024-01-15 10:30:00',
      batchId: 'batch-1',
      batchName: '新年推广批次',
      usageCount: 2,
      status: 'active',
      priority: 3,
      shortCodeId: '1'
    },
    {
      id: '2',
      type: 'WhatsApp',
      content: '254751076497',
      addedAt: '2024-01-15 10:31:00',
      batchId: 'batch-1',
      batchName: '新年推广批次',
      usageCount: 1,
      status: 'active',
      priority: 3,
      shortCodeId: '1'
    },
    {
      id: '3',
      type: 'WhatsApp',
      content: '254751076498',
      addedAt: '2024-01-14 15:20:00',
      batchId: 'batch-2',
      batchName: '客服账号组',
      usageCount: 12,
      status: 'active',
      priority: 2,
      shortCodeId: '1'
    },
    {
      id: '4',
      type: 'Telegram',
      content: 'newservice2024',
      addedAt: '2024-01-13 09:15:00',
      batchId: 'batch-3',
      batchName: 'TG频道集合',
      usageCount: 5,
      status: 'active',
      priority: 1,
      shortCodeId: '2'
    },
    // 增加更多测试数据
    ...Array.from({ length: 25 }, (_, i) => ({
      id: `test-${i + 5}`,
      type: 'WhatsApp' as const,
      content: `25475107${6499 + i}`,
      addedAt: `2024-01-${10 + (i % 15)} 10:30:00`,
      batchId: 'batch-1',
      batchName: '新年推广批次',
      usageCount: Math.floor(Math.random() * 10),
      status: 'active' as const,
      priority: 3,
      shortCodeId: '1'
    }))
  ];

  // 根据选择的活码过滤数据
  const getFilteredData = () => {
    let filtered = linkData;

    // 根据选择的活码过滤
    if (selectedShortCode) {
      filtered = filtered.filter(item => item.shortCodeId === selectedShortCode);
      
      // 根据活码类型自动设置类型过滤
      const shortCode = shortCodes.find(sc => sc.id === selectedShortCode);
      if (shortCode && shortCode.type !== selectedType) {
        setSelectedType(shortCode.type);
      }
    }

    // 根据类型过滤
    filtered = filtered.filter(item => item.type === selectedType);

    // 根据搜索词过滤
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.content.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filtered;
  };

  const filteredData = getFilteredData();

  // 分页计算
  const totalItems = filteredData.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentPageData = filteredData
    .sort((a, b) => {
      if (a.priority !== b.priority) return b.priority - a.priority;
      return new Date(a.addedAt).getTime() - new Date(b.addedAt).getTime();
    })
    .slice(startIndex, endIndex);

  // 重置分页当过滤条件改变时
  useEffect(() => {
    setCurrentPage(1);
  }, [selectedShortCode, selectedType, searchTerm, itemsPerPage]);

  // 获取当前选择活码的批次数据
  const getCurrentBatches = () => {
    if (!selectedShortCode) return [];
    return linkBatches.filter(batch => batch.shortCodeId === selectedShortCode);
  };

  const priorityBatches = getCurrentBatches().filter(batch => batch.status === 'priority');

  // 获取已完成的批次（用于显示完成状态）
  const getCompletedBatches = () => {
    return getCurrentBatches().filter(batch => 
      batch.status === 'normal' && batch.usedCount >= batch.priorityCount
    );
  };

  const completedBatches = getCompletedBatches();

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'WhatsApp':
        return <MessageCircle className="h-4 w-4 text-green-600" />;
      case 'Telegram':
        return <Send className="h-4 w-4 text-blue-600" />;
      case '网址':
        return <Globe className="h-4 w-4 text-purple-600" />;
      default:
        return <Globe className="h-4 w-4" />;
    }
  };

  const getBatchPlaceholder = () => {
    switch (selectedType) {
      case 'WhatsApp':
        return '请输入多个账号，每行一个或用逗号分隔\n例如：\n254751076496\n254751076497';
      case 'Telegram':
        return '请输入多个号码，每行一个或用逗号分隔\n例如：\nnewservice2024\ncustomercare';
      case '网址':
        return '请输入多个URL地址，每行一个或用逗号分隔\n例如：\nhttps://example1.com\nhttps://example2.com';
      default:
        return '请输入内容';
    }
  };

  // 模拟批次状态自动转换逻辑
  const simulateBatchStatusTransition = () => {
    // 当插入新批次时，将上一批次"优先轮询"数据自动更改为"常规轮询"
    const currentPriorityBatches = getCurrentBatches().filter(b => b.status === 'priority');
    if (currentPriorityBatches.length > 0) {
      toast.info('上一批次优先轮询已转为常规轮询');
      console.log('批次状态转换:', currentPriorityBatches.map(b => b.id));
    }
  };

  const handleAddBatch = () => {
    if (!batchContent.trim() || !batchName.trim()) {
      toast.error('请填写批次名称和内容');
      return;
    }

    if (!selectedShortCode) {
      toast.error('请选择一个活码');
      return;
    }

    const items = batchContent.split(/[,\n]/).map(item => item.trim()).filter(item => item);
    
    // 执行批次状态转换
    simulateBatchStatusTransition();
    
    toast.success(`批次 "${batchName}" 创建成功，添加了 ${items.length} 个链接！`);
    
    console.log('批量添加新批次:', {
      shortCodeId: selectedShortCode,
      shortCodeTitle: shortCodes.find(sc => sc.id === selectedShortCode)?.title,
      name: batchName,
      type: selectedType,
      items: items,
      priorityCount: parseInt(batchPriorityCount)
    });
    
    setBatchContent('');
    setBatchName('');
    setBatchPriorityCount('5');
    setIsBatchDialogOpen(false);
  };

  const handleDeleteSelected = () => {
    toast.success(`已删除选中的 ${selectedItems.length} 个链接`);
    console.log('删除选中项:', selectedItems);
    setSelectedItems([]);
  };

  const handleDeleteAll = () => {
    const currentShortCode = shortCodes.find(sc => sc.id === selectedShortCode);
    toast.success(`已删除活码 "${currentShortCode?.title}" 的所有 ${selectedType} 链接`);
    console.log('删除全部:', selectedShortCode, selectedType);
  };

  const handleDeleteByInput = () => {
    if (!deleteInput.trim()) {
      toast.error('请输入要删除的内容');
      return;
    }

    const inputItems = deleteInput.split(/[,\n\s]/).map(item => item.trim()).filter(item => item);
    toast.success(`已删除匹配的 ${inputItems.length} 个链接`);
    console.log('根据输入删除:', inputItems);
    setDeleteInput('');
  };

  const handleItemSelect = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedItems([...selectedItems, id]);
    } else {
      setSelectedItems(selectedItems.filter(item => item !== id));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(currentPageData.map(item => item.id));
    } else {
      setSelectedItems([]);
    }
  };

  const simulatePolling = () => {
    if (!selectedShortCode) {
      toast.error('请先选择一个活码');
      return;
    }

    const sortedForPolling = filteredData
      .filter(item => item.status === 'active')
      .sort((a, b) => {
        if (a.priority !== b.priority) return b.priority - a.priority;
        return new Date(a.addedAt).getTime() - new Date(b.addedAt).getTime();
      });

    if (sortedForPolling.length > 0) {
      const nextLink = sortedForPolling[0];
      const baseUrl = `https://short.ly/${shortCodes.find(sc => sc.id === selectedShortCode)?.shortCode}`;
      toast.success(`轮询跳转: ${baseUrl} -> ${nextLink.content}`);
      console.log('轮询跳转:', nextLink);
    } else {
      toast.error('没有可用的链接进行轮询');
    }
  };

  // 分页控制函数
  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  const goToFirstPage = () => goToPage(1);
  const goToLastPage = () => goToPage(totalPages);
  const goToPrevPage = () => goToPage(currentPage - 1);
  const goToNextPage = () => goToPage(currentPage + 1);

  // 统计数据
  const stats = {
    whatsapp: linkData.filter(item => item.type === 'WhatsApp' && (!selectedShortCode || item.shortCodeId === selectedShortCode)).length,
    telegram: linkData.filter(item => item.type === 'Telegram' && (!selectedShortCode || item.shortCodeId === selectedShortCode)).length,
    website: linkData.filter(item => item.type === '网址' && (!selectedShortCode || item.shortCodeId === selectedShortCode)).length,
    priorityBatches: priorityBatches.length,
    totalUsage: filteredData.reduce((sum, item) => sum + item.usageCount, 0)
  };

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl">多类型链接轮询跳转</h1>
          <p className="text-muted-foreground">
            为活码配置多种类型的链接数据，支持批次优先轮询机制
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={simulatePolling}>
            <Target className="mr-2 h-4 w-4" />
            模拟轮询
          </Button>
          <Dialog open={isBatchDialogOpen} onOpenChange={setIsBatchDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Upload className="mr-2 h-4 w-4" />
                批量添加
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[525px]">
              <DialogHeader>
                <DialogTitle>创建新批次</DialogTitle>
                <DialogDescription>
                  为选择的活码添加新的链接批次，设置优先轮询次数
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label>选择活码 *</Label>
                  <Select value={selectedShortCode} onValueChange={setSelectedShortCode}>
                    <SelectTrigger>
                      <SelectValue placeholder="请选择活码" />
                    </SelectTrigger>
                    <SelectContent>
                      {shortCodes.map(sc => (
                        <SelectItem key={sc.id} value={sc.id}>
                          <div className="flex items-center space-x-2">
                            {getTypeIcon(sc.type)}
                            <span>{sc.title} (/{sc.shortCode})</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {selectedShortCode && (
                    <div className="text-sm text-muted-foreground p-2 bg-muted rounded">
                      <strong>当前选中活码：</strong>{shortCodes.find(sc => sc.id === selectedShortCode)?.title}
                    </div>
                  )}
                </div>
                <div className="space-y-2">
                  <Label>批次名称 *</Label>
                  <Input
                    placeholder="输入批次名称"
                    value={batchName}
                    onChange={(e) => setBatchName(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label>优先轮询次数</Label>
                  <Select value={batchPriorityCount} onValueChange={setBatchPriorityCount}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(num => (
                        <SelectItem key={num} value={num.toString()}>
                          {num}次
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>批量内容 *</Label>
                  <Textarea
                    placeholder={getBatchPlaceholder()}
                    value={batchContent}
                    onChange={(e) => setBatchContent(e.target.value)}
                    rows={8}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsBatchDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleAddBatch}>
                  创建批次
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* 活码选择 */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-4">
            <Label>选择活码:</Label>
            <Select value={selectedShortCode} onValueChange={setSelectedShortCode}>
              <SelectTrigger className="w-64">
                <SelectValue placeholder="请选择要管理的活码" />
              </SelectTrigger>
              <SelectContent>
                {shortCodes.map(sc => (
                  <SelectItem key={sc.id} value={sc.id}>
                    <div className="flex items-center space-x-2">
                      {getTypeIcon(sc.type)}
                      <span>{sc.title}</span>
                      <Badge variant="outline">/{sc.shortCode}</Badge>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {selectedShortCode && (
              <Badge variant="secondary">
                已选择: {shortCodes.find(sc => sc.id === selectedShortCode)?.title}
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">WhatsApp</CardTitle>
            <MessageCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.whatsapp}</div>
            <p className="text-xs text-muted-foreground">
              账号数量
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Telegram</CardTitle>
            <Send className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.telegram}</div>
            <p className="text-xs text-muted-foreground">
              号码数量
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">网址</CardTitle>
            <Globe className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.website}</div>
            <p className="text-xs text-muted-foreground">
              URL 数量
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">优先批次</CardTitle>
            <RotateCcw className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.priorityBatches}</div>
            <p className="text-xs text-muted-foreground">
              优先轮询中的批次
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总使用次数</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalUsage}</div>
            <p className="text-xs text-muted-foreground">
              累计轮询次数
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 批次优先级状态 */}
      {selectedShortCode && (priorityBatches.length > 0 || completedBatches.length > 0) && (
        <Card>
          <CardHeader>
            <CardTitle>批次优先级状态</CardTitle>
            <CardDescription>
              当前活码的批次状态及其进度
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* 优先轮询批次 */}
              {priorityBatches.map((batch) => (
                <div key={batch.id} className="flex items-center justify-between p-4 border rounded-lg bg-blue-50/50">
                  <div className="flex items-center space-x-4">
                    {getTypeIcon(batch.linkType)}
                    <div>
                      <p className="font-medium">{batch.name}</p>
                      <p className="text-sm text-muted-foreground">
                        优先级: {batch.priority} | 创建时间: {batch.createdAt}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <p className="text-sm font-medium">
                        {batch.usedCount}/{batch.priorityCount}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        剩余 {batch.priorityCount - batch.usedCount} 次
                      </p>
                    </div>
                    <div className="w-20 bg-secondary rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full" 
                        style={{ width: `${(batch.usedCount / batch.priorityCount) * 100}%` }}
                      />
                    </div>
                    <Badge variant="outline" className="bg-blue-100 text-blue-800">
                      优先轮询中
                    </Badge>
                  </div>
                </div>
              ))}
              
              {/* 已完成的批次 */}
              {completedBatches.map((batch) => (
                <div key={batch.id} className="flex items-center justify-between p-4 border rounded-lg bg-green-50/50">
                  <div className="flex items-center space-x-4">
                    {getTypeIcon(batch.linkType)}
                    <div>
                      <p className="font-medium">{batch.name}</p>
                      <p className="text-sm text-muted-foreground">
                        优先级: {batch.priority} | 创建时间: {batch.createdAt}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <p className="text-sm font-medium">
                        {batch.usedCount}/{batch.priorityCount}
                      </p>
                      <p className="text-xs text-green-600">
                        优先轮询已完成
                      </p>
                    </div>
                    <div className="w-20 bg-secondary rounded-full h-2">
                      <div className="bg-green-500 h-2 rounded-full w-full" />
                    </div>
                    <Badge variant="outline" className="bg-green-100 text-green-800">
                      已转常规轮询
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 搜索栏 */}
      {selectedShortCode && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder={`搜索 ${selectedType} 内容...`}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 数据列表 */}
      {selectedShortCode ? (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>{selectedType} 数据列表</CardTitle>
                <CardDescription>
                  活码 "{shortCodes.find(sc => sc.id === selectedShortCode)?.title}" 的 {selectedType} 链接数据 (共 {totalItems} 条)
                </CardDescription>
              </div>
              <div className="flex space-x-2">
                {selectedItems.length > 0 && (
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="destructive" size="sm">
                        <Trash2 className="mr-2 h-4 w-4" />
                        删除选中 ({selectedItems.length})
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>确认删除选中数据</AlertDialogTitle>
                        <AlertDialogDescription>
                          您确定要删除选中的 {selectedItems.length} 条数据吗？删除后不可恢复。
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>取消</AlertDialogCancel>
                        <AlertDialogAction onClick={handleDeleteSelected} className="bg-destructive text-destructive-foreground">
                          确认删除
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                )}
                
                <Dialog>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm">
                      <Edit className="mr-2 h-4 w-4" />
                      输入删除
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>根据内容删除</DialogTitle>
                      <DialogDescription>
                        输入要删除的内容，支持多个（逗号或换行分隔）
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                      <Textarea
                        placeholder={`输入要删除的${selectedType}内容...\n例如：\n${selectedType === 'WhatsApp' ? '254751076496, 254751076497' : selectedType === 'Telegram' ? 'username1, username2' : 'https://example1.com'}`}
                        value={deleteInput}
                        onChange={(e) => setDeleteInput(e.target.value)}
                        rows={4}
                      />
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setDeleteInput('')}>
                        取消
                      </Button>
                      <Button onClick={handleDeleteByInput} variant="destructive">
                        删除匹配项
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>

                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="outline" size="sm">
                      <Trash2 className="mr-2 h-4 w-4" />
                      全部删除
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>确认删除全部 {selectedType} 数据</AlertDialogTitle>
                      <AlertDialogDescription>
                        您确定要删除活码 "{shortCodes.find(sc => sc.id === selectedShortCode)?.title}" 的全部 {selectedType} 数据吗？删除后不可恢复。
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>取消</AlertDialogCancel>
                      <AlertDialogAction onClick={handleDeleteAll} className="bg-destructive text-destructive-foreground">
                        确认删除
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
                
                <Button variant="outline" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  导出数据
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* 分页控件 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Label className="text-sm">每页显示:</Label>
                  <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemsPerPage(Number(value))}>
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="5">5</SelectItem>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="20">20</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>
                  <span className="text-sm text-muted-foreground">
                    共 {totalItems} 条
                  </span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={goToFirstPage}
                    disabled={currentPage === 1}
                  >
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={goToPrevPage}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <span className="text-sm text-muted-foreground">
                    第 {currentPage} 页，共 {totalPages} 页
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={goToNextPage}
                    disabled={currentPage === totalPages}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={goToLastPage}
                    disabled={currentPage === totalPages}
                  >
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* 数据表格 */}
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectedItems.length === currentPageData.length && currentPageData.length > 0}
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                    <TableHead>编号</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>内容</TableHead>
                    <TableHead>批次信息</TableHead>
                    <TableHead>使用统计</TableHead>
                    <TableHead>轮询状态</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {currentPageData.map((item, index) => (
                    <TableRow key={item.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedItems.includes(item.id)}
                          onCheckedChange={(checked) => handleItemSelect(item.id, checked as boolean)}
                        />
                      </TableCell>
                      <TableCell>{startIndex + index + 1}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getTypeIcon(item.type)}
                          <span>{item.type}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-mono text-sm">
                          {item.content}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <Badge variant="outline" className="mb-1">
                            {item.batchName}
                          </Badge>
                          <div className="text-xs text-muted-foreground">
                            优先级: {item.priority}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-center">
                          <div className="font-medium">{item.usageCount}</div>
                          <div className="text-xs text-muted-foreground">次</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge 
                          variant={item.status === 'active' ? 'default' : 'secondary'}
                          className={item.status === 'active' ? 'bg-green-100 text-green-800' : ''}
                        >
                          {item.status === 'active' ? '正常' : '禁用'}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* 底部分页信息 */}
              <div className="flex items-center justify-between text-sm text-muted-foreground">
                <div>
                  显示第 {startIndex + 1} 到 {Math.min(endIndex, totalItems)} 条，共 {totalItems} 条数据
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={goToPrevPage}
                    disabled={currentPage === 1}
                  >
                    上一页
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={goToNextPage}
                    disabled={currentPage === totalPages}
                  >
                    下一页
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-muted-foreground">
              <LinkIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>请先选择一个活码来管理链接数据</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}