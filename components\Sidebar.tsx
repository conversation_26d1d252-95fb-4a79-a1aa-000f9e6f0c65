import React from 'react';
import { <PERSON><PERSON> } from './ui/button';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { Badge } from './ui/badge';
import { Separator } from './ui/separator';
import { 
  LayoutDashboard, 
  Link, 
  Activity, 
  Users, 
  Globe, 
  RotateCcw, 
  FileText, 
  LogOut,
  Settings
} from 'lucide-react';

type PageType = 'dashboard' | 'short-codes' | 'access-records' | 'user-management' | 'domain-management' | 'link-polling' | 'operation-logs';

interface User {
  id: string;
  name: string;
  email: string;
  role: 'super-admin' | 'boss' | 'manager';
  avatar?: string;
}

interface SidebarProps {
  user: User;
  currentPage: PageType;
  onPageChange: (page: PageType) => void;
  onLogout: () => void;
}

const roleLabels = {
  'super-admin': '超级管理员',
  'boss': 'Boss',
  'manager': '经理'
};

const roleColors = {
  'super-admin': 'bg-red-500',
  'boss': 'bg-blue-500', 
  'manager': 'bg-green-500'
};

export function Sidebar({ user, currentPage, onPageChange, onLogout }: SidebarProps) {
  const menuItems = [
    {
      id: 'dashboard' as PageType,
      label: '工作台',
      icon: LayoutDashboard,
      roles: ['super-admin', 'boss', 'manager']
    },
    {
      id: 'short-codes' as PageType,
      label: '短码管理',
      icon: Link,
      roles: ['super-admin', 'boss', 'manager']
    },
    {
      id: 'access-records' as PageType,
      label: '访问记录',
      icon: Activity,
      roles: ['super-admin', 'boss', 'manager']
    },
    {
      id: 'user-management' as PageType,
      label: '用户管理',
      icon: Users,
      roles: ['super-admin', 'boss']
    },
    {
      id: 'domain-management' as PageType,
      label: '域名管理',
      icon: Globe,
      roles: ['super-admin']
    },
    {
      id: 'link-polling' as PageType,
      label: '链接轮询',
      icon: RotateCcw,
      roles: ['super-admin', 'boss', 'manager']
    },
    {
      id: 'operation-logs' as PageType,
      label: '操作日志',
      icon: FileText,
      roles: ['super-admin', 'boss']
    }
  ];

  const availableMenuItems = menuItems.filter(item => 
    item.roles.includes(user.role)
  );

  return (
    <div className="w-64 bg-card border-r border-border/50 flex flex-col shadow-sm">
      {/* 用户信息 */}
      <div className="p-6 border-b border-border/30">
        <div className="flex items-center space-x-3">
          <Avatar className="h-11 w-11 ring-2 ring-offset-2 ring-offset-background ring-border/20">
            <AvatarImage src={user.avatar} />
            <AvatarFallback className={`${roleColors[user.role]} text-white`}>
              {user.name.charAt(0)}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <p className="font-medium truncate mb-1">{user.name}</p>
            <Badge variant="secondary" className="text-xs px-2 py-0.5 rounded-full">
              {roleLabels[user.role]}
            </Badge>
          </div>
        </div>
      </div>

      {/* 导航菜单 */}
      <nav className="flex-1 p-4">
        <div className="space-y-1">
          {availableMenuItems.map((item) => {
            const Icon = item.icon;
            const isActive = currentPage === item.id;
            
            return (
              <Button
                key={item.id}
                variant={isActive ? "secondary" : "ghost"}
                className={`w-full justify-start h-10 px-3 transition-all duration-200 ${
                  isActive 
                    ? "bg-secondary/80 text-secondary-foreground shadow-sm" 
                    : "hover:bg-accent/50 hover:translate-x-0.5"
                }`}
                onClick={() => onPageChange(item.id)}
              >
                <Icon className={`mr-3 h-4 w-4 ${isActive ? "text-primary" : ""}`} />
                <span className="font-medium">{item.label}</span>
              </Button>
            );
          })}
        </div>
      </nav>

      {/* 底部操作 */}
      <div className="p-4 space-y-1 border-t border-border/30">
        <Button variant="ghost" className="w-full justify-start h-9 px-3 text-muted-foreground hover:text-foreground transition-colors">
          <Settings className="mr-3 h-4 w-4" />
          <span className="font-medium">系统设置</span>
        </Button>
        <Button 
          variant="ghost" 
          className="w-full justify-start h-9 px-3 text-muted-foreground hover:text-destructive hover:bg-destructive/5 transition-all duration-200"
          onClick={onLogout}
        >
          <LogOut className="mr-3 h-4 w-4" />
          <span className="font-medium">退出登录</span>
        </Button>
      </div>
    </div>
  );
}