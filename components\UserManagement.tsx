import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { But<PERSON> } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Label } from './ui/label';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from './ui/dropdown-menu';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from './ui/alert-dialog';
import { 
  Plus, 
  Search, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  User as UserIcon,
  Shield,
  Users,
  Eye,
  UserCheck,
  UserX,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react';
import { toast } from 'sonner@2.0.3';

interface User {
  id: string;
  name: string;
  role: 'super-admin' | 'boss' | 'manager';
}

interface SystemUser {
  id: string;
  username: string;
  email: string;
  role: 'super-admin' | 'boss' | 'manager';
  status: 'active' | 'inactive';
  createdAt: string;
  lastLogin: string;
  shortCodesCount: number;
  createdBy: string;
}

interface UserManagementProps {
  user: User;
}

export function UserManagement({ user }: UserManagementProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<SystemUser | null>(null);
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const [newUser, setNewUser] = useState({
    username: '',
    email: '',
    password: '',
    role: 'manager' as const
  });

  // 模拟数据
  const mockUsers: SystemUser[] = [
    {
      id: '1',
      username: '超级管理员',
      email: '<EMAIL>',
      role: 'super-admin',
      status: 'active',
      createdAt: '2024-01-01 10:00:00',
      lastLogin: '2024-01-15 14:30:00',
      shortCodesCount: 156,
      createdBy: 'system'
    },
    {
      id: '2',
      username: '张总',
      email: '<EMAIL>',
      role: 'boss',
      status: 'active',
      createdAt: '2024-01-02 09:30:00',
      lastLogin: '2024-01-15 13:45:00',
      shortCodesCount: 89,
      createdBy: '超级管理员'
    },
    {
      id: '3',
      username: '李经理',
      email: '<EMAIL>',
      role: 'manager',
      status: 'active',
      createdAt: '2024-01-03 14:20:00',
      lastLogin: '2024-01-15 11:20:00',
      shortCodesCount: 23,
      createdBy: '张总'
    },
    {
      id: '4',
      username: '王经理',
      email: '<EMAIL>',
      role: 'manager',
      status: 'active',
      createdAt: '2024-01-05 16:15:00',
      lastLogin: '2024-01-14 15:30:00',
      shortCodesCount: 45,
      createdBy: '张总'
    },
    {
      id: '5',
      username: '刘专员',
      email: '<EMAIL>',
      role: 'manager',
      status: 'inactive',
      createdAt: '2024-01-08 11:40:00',
      lastLogin: '2024-01-10 09:15:00',
      shortCodesCount: 12,
      createdBy: '李经理'
    },
    // 添加更多测试数据
    ...Array.from({ length: 20 }, (_, i) => ({
      id: `test-${i + 6}`,
      username: `测试用户${i + 1}`,
      email: `test${i + 1}@example.com`,
      role: ['manager', 'boss'][i % 2] as 'manager' | 'boss',
      status: ['active', 'inactive'][i % 3 === 0 ? 1 : 0] as 'active' | 'inactive',
      createdAt: `2024-01-${10 + (i % 20)} 10:30:00`,
      lastLogin: `2024-01-${12 + (i % 15)} 14:20:00`,
      shortCodesCount: Math.floor(Math.random() * 50),
      createdBy: ['张总', '李经理', '超级管理员'][i % 3]
    }))
  ];

  // 根据权限过滤用户数据
  const getFilteredUsers = () => {
    let filtered = mockUsers;

    // 根据用户角色过滤可见数据
    if (user.role === 'boss') {
      // Boss只能看到自己创建的用户和下级
      filtered = mockUsers.filter(u => 
        u.id === user.id || 
        u.createdBy === user.name || 
        u.role === 'manager'
      );
    } else if (user.role === 'manager') {
      // Manager只能看到自己
      filtered = mockUsers.filter(u => u.id === user.id);
    }

    // 搜索过滤
    if (searchTerm) {
      filtered = filtered.filter(u =>
        u.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        u.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // 角色过滤
    if (roleFilter !== 'all') {
      filtered = filtered.filter(u => u.role === roleFilter);
    }

    // 状态过滤
    if (statusFilter !== 'all') {
      filtered = filtered.filter(u => u.status === statusFilter);
    }

    return filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  };

  const filteredUsers = getFilteredUsers();

  // 分页计算
  const totalItems = filteredUsers.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentPageData = filteredUsers.slice(startIndex, endIndex);

  // 重置分页当过滤条件改变时
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, roleFilter, statusFilter, itemsPerPage]);

  // 分页控制函数
  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  const goToFirstPage = () => goToPage(1);
  const goToLastPage = () => goToPage(totalPages);
  const goToPrevPage = () => goToPage(currentPage - 1);
  const goToNextPage = () => goToPage(currentPage + 1);

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'super-admin':
        return <Shield className="h-4 w-4 text-red-600" />;
      case 'boss':
        return <Shield className="h-4 w-4 text-blue-600" />;
      case 'manager':
        return <UserIcon className="h-4 w-4 text-green-600" />;
      default:
        return <UserIcon className="h-4 w-4" />;
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'super-admin':
        return '超级管理员';
      case 'boss':
        return 'Boss';
      case 'manager':
        return '经理';
      default:
        return role;
    }
  };

  const handleCreate = () => {
    if (!newUser.username || !newUser.email || !newUser.password) {
      toast.error('请填写所有必填字段');
      return;
    }

    toast.success(`用户 "${newUser.username}" 创建成功！`);
    console.log('创建用户:', newUser);
    
    setNewUser({
      username: '',
      email: '',
      password: '',
      role: 'manager'
    });
    setIsCreateDialogOpen(false);
  };

  const handleEdit = (systemUser: SystemUser) => {
    setEditingUser(systemUser);
    setIsEditDialogOpen(true);
  };

  const handleSaveEdit = () => {
    if (!editingUser) return;
    
    toast.success(`用户 "${editingUser.username}" 信息已更新`);
    console.log('更新用户:', editingUser);
    setIsEditDialogOpen(false);
  };

  const handleDelete = (systemUser: SystemUser) => {
    toast.success(`用户 "${systemUser.username}" 已删除`);
    console.log('删除用户:', systemUser.id);
  };

  const handleToggleStatus = (systemUser: SystemUser) => {
    const newStatus = systemUser.status === 'active' ? 'inactive' : 'active';
    toast.success(`用户 "${systemUser.username}" 已${newStatus === 'active' ? '启用' : '禁用'}`);
    console.log('切换用户状态:', systemUser.id, newStatus);
  };

  // 权限检查
  const canCreateUser = user.role === 'super-admin' || user.role === 'boss';
  const canEditUser = (targetUser: SystemUser) => {
    if (user.role === 'super-admin') return true;
    if (user.role === 'boss' && targetUser.role === 'manager') return true;
    return false;
  };

  const stats = {
    total: filteredUsers.length,
    active: filteredUsers.filter(u => u.status === 'active').length,
    inactive: filteredUsers.filter(u => u.status === 'inactive').length,
    totalShortCodes: filteredUsers.reduce((sum, u) => sum + u.shortCodesCount, 0)
  };

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl">用户管理</h1>
          <p className="text-muted-foreground">
            管理系统用户账户和权限
          </p>
        </div>
        {canCreateUser && (
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                创建用户
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>创建新用户</DialogTitle>
                <DialogDescription>
                  为系统添加新的用户账户
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label>用户名 *</Label>
                  <Input
                    placeholder="输入用户名"
                    value={newUser.username}
                    onChange={(e) => setNewUser(prev => ({...prev, username: e.target.value}))}
                  />
                </div>
                <div className="space-y-2">
                  <Label>邮箱 *</Label>
                  <Input
                    type="email"
                    placeholder="输入邮箱地址"
                    value={newUser.email}
                    onChange={(e) => setNewUser(prev => ({...prev, email: e.target.value}))}
                  />
                </div>
                <div className="space-y-2">
                  <Label>密码 *</Label>
                  <Input
                    type="password"
                    placeholder="输入初始密码"
                    value={newUser.password}
                    onChange={(e) => setNewUser(prev => ({...prev, password: e.target.value}))}
                  />
                </div>
                <div className="space-y-2">
                  <Label>角色</Label>
                  <Select 
                    value={newUser.role} 
                    onValueChange={(value: 'boss' | 'manager') => 
                      setNewUser(prev => ({...prev, role: value}))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {user.role === 'super-admin' && (
                        <SelectItem value="boss">
                          <div className="flex items-center space-x-2">
                            <Shield className="h-4 w-4 text-blue-600" />
                            <span>Boss</span>
                          </div>
                        </SelectItem>
                      )}
                      <SelectItem value="manager">
                        <div className="flex items-center space-x-2">
                          <UserIcon className="h-4 w-4 text-green-600" />
                          <span>经理</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleCreate}>
                  创建用户
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">用户总数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              系统注册用户
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃用户</CardTitle>
            <UserCheck className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.active}</div>
            <p className="text-xs text-muted-foreground">
              正常使用中
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">禁用用户</CardTitle>
            <UserX className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.inactive}</div>
            <p className="text-xs text-muted-foreground">
              已停用账户
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活码总数</CardTitle>
            <Eye className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalShortCodes}</div>
            <p className="text-xs text-muted-foreground">
              用户创建的活码
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和过滤 */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="lg:col-span-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索用户名或邮箱..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            <div>
              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="选择角色" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部角色</SelectItem>
                  <SelectItem value="super-admin">超级管理员</SelectItem>
                  <SelectItem value="boss">Boss</SelectItem>
                  <SelectItem value="manager">经理</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="active">活跃</SelectItem>
                  <SelectItem value="inactive">禁用</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 用户列表 */}
      <Card>
        <CardHeader>
          <CardTitle>用户列表 (共 {totalItems} 个用户)</CardTitle>
          <CardDescription>
            系统用户账户管理
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* 分页控件 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Label className="text-sm">每页显示:</Label>
                <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemsPerPage(Number(value))}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
                <span className="text-sm text-muted-foreground">
                  共 {totalItems} 条
                </span>
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToFirstPage}
                  disabled={currentPage === 1}
                >
                  <ChevronsLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPrevPage}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="text-sm text-muted-foreground">
                  第 {currentPage} 页，共 {totalPages} 页
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNextPage}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToLastPage}
                  disabled={currentPage === totalPages}
                >
                  <ChevronsRight className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* 数据表格 */}
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>用户名</TableHead>
                  <TableHead>邮箱</TableHead>
                  <TableHead>角色</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>活码数量</TableHead>
                  <TableHead>最后登录</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentPageData.map((systemUser) => (
                  <TableRow key={systemUser.id}>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {getRoleIcon(systemUser.role)}
                        <span className="font-medium">{systemUser.username}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">{systemUser.email}</span>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {getRoleLabel(systemUser.role)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={systemUser.status === 'active' ? 'default' : 'secondary'}>
                        {systemUser.status === 'active' ? '活跃' : '禁用'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className="font-medium">{systemUser.shortCodesCount}</span>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-muted-foreground">{systemUser.lastLogin}</span>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-muted-foreground">{systemUser.createdAt}</span>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {canEditUser(systemUser) && (
                            <DropdownMenuItem onClick={() => handleEdit(systemUser)}>
                              <Edit className="mr-2 h-4 w-4" />
                              编辑
                            </DropdownMenuItem>
                          )}
                          {canEditUser(systemUser) && (
                            <DropdownMenuItem onClick={() => handleToggleStatus(systemUser)}>
                              {systemUser.status === 'active' ? (
                                <>
                                  <UserX className="mr-2 h-4 w-4" />
                                  禁用
                                </>
                              ) : (
                                <>
                                  <UserCheck className="mr-2 h-4 w-4" />
                                  启用
                                </>
                              )}
                            </DropdownMenuItem>
                          )}
                          {canEditUser(systemUser) && systemUser.role !== 'super-admin' && (
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <DropdownMenuItem 
                                  className="text-destructive"
                                  onSelect={(e) => e.preventDefault()}
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  删除
                                </DropdownMenuItem>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>确认删除用户</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    您确定要删除用户 "{systemUser.username}" 吗？
                                    {systemUser.shortCodesCount > 0 && (
                                      <span className="text-destructive">
                                        <br />警告：该用户拥有 {systemUser.shortCodesCount} 个活码，删除后这些活码将被转移或删除。
                                      </span>
                                    )}
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>取消</AlertDialogCancel>
                                  <AlertDialogAction 
                                    onClick={() => handleDelete(systemUser)}
                                    className="bg-destructive text-destructive-foreground"
                                  >
                                    确认删除
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {/* 底部分页信息 */}
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <div>
                显示第 {startIndex + 1} 到 {Math.min(endIndex, totalItems)} 条，共 {totalItems} 条数据
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPrevPage}
                  disabled={currentPage === 1}
                >
                  上一页
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNextPage}
                  disabled={currentPage === totalPages}
                >
                  下一页
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 编辑用户对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>编辑用户信息</DialogTitle>
            <DialogDescription>
              修改用户基本信息和角色权限
            </DialogDescription>
          </DialogHeader>
          {editingUser && (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label>用户名</Label>
                <Input
                  value={editingUser.username}
                  onChange={(e) => setEditingUser(prev => prev ? {...prev, username: e.target.value} : null)}
                />
              </div>
              <div className="space-y-2">
                <Label>邮箱</Label>
                <Input
                  type="email"
                  value={editingUser.email}
                  onChange={(e) => setEditingUser(prev => prev ? {...prev, email: e.target.value} : null)}
                />
              </div>
              <div className="space-y-2">
                <Label>角色</Label>
                <Select 
                  value={editingUser.role} 
                  onValueChange={(value: 'boss' | 'manager') => 
                    setEditingUser(prev => prev ? {...prev, role: value} : null)}
                  disabled={editingUser.role === 'super-admin'}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {user.role === 'super-admin' && (
                      <SelectItem value="boss">
                        <div className="flex items-center space-x-2">
                          <Shield className="h-4 w-4 text-blue-600" />
                          <span>Boss</span>
                        </div>
                      </SelectItem>
                    )}
                    <SelectItem value="manager">
                      <div className="flex items-center space-x-2">
                        <UserIcon className="h-4 w-4 text-green-600" />
                        <span>经理</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSaveEdit}>
              保存修改
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}