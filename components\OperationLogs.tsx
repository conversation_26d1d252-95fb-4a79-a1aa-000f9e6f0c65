import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { But<PERSON> } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Label } from './ui/label';
import { 
  Search, 
  Calendar, 
  User, 
  Activity, 
  Download,
  Clock,
  Shield,
  Plus,
  Edit,
  Trash2,
  Eye,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react';
import { User as UserType, OperationLog } from '../types';
import { TIME_FILTER_OPTIONS, ROLE_FILTER_OPTIONS, ROLE_LABELS } from '../constants';
import { 
  filterLogsByPermission,
  filterLogsBySearch, 
  filterLogsByAction,
  filterLogsByUser,
  filterLogsByTime,
  sortLogsByDate,
  calculateLogStats,
  getUniqueActions,
  getMockOperationLogs
} from '../constants/operationLogHelpers';
import { toast } from 'sonner@2.0.3';

interface OperationLogsProps {
  user: UserType;
}

export function OperationLogs({ user }: OperationLogsProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [actionFilter, setActionFilter] = useState('all');
  const [userFilter, setUserFilter] = useState('all');
  const [timeFilter, setTimeFilter] = useState('all');
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  // Fallback constants in case imports fail
  const fallbackRoleLabels = {
    'super-admin': '超级管理员',
    'boss': 'Boss',
    'manager': '经理'
  };

  const fallbackTimeFilterOptions = [
    { value: 'all', label: '全部时间' },
    { value: 'today', label: '今天' },
    { value: 'week', label: '最近7天' },
    { value: 'month', label: '最近30天' }
  ];

  const fallbackRoleFilterOptions = [
    { value: 'all', label: '全部角色' },
    { value: 'boss', label: 'Boss' },
    { value: 'manager', label: '经理' }
  ];

  // Get filtered logs
  const getFilteredLogs = () => {
    try {
      const mockLogs = getMockOperationLogs(user) || [];
      let filtered = filterLogsByPermission(mockLogs, user) || [];
      filtered = filterLogsBySearch(filtered, searchTerm) || [];
      filtered = filterLogsByAction(filtered, actionFilter) || [];
      filtered = filterLogsByUser(filtered, userFilter) || [];
      filtered = filterLogsByTime(filtered, timeFilter) || [];
      return sortLogsByDate(filtered) || [];
    } catch (error) {
      console.error('Error filtering logs:', error);
      return [];
    }
  };

  const filteredData = getFilteredLogs() || [];
  
  // 分页计算
  const totalItems = filteredData.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentPageData = filteredData.slice(startIndex, endIndex);

  // 重置分页当过滤条件改变时
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, actionFilter, userFilter, timeFilter, itemsPerPage]);

  // 分页控制函数
  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  const goToFirstPage = () => goToPage(1);
  const goToLastPage = () => goToPage(totalPages);
  const goToPrevPage = () => goToPage(currentPage - 1);
  const goToNextPage = () => goToPage(currentPage + 1);

  const stats = calculateLogStats ? calculateLogStats(filteredData || []) : { total: 0, today: 0, success: 0, failed: 0, warning: 0 };
  const uniqueActions = getUniqueActions ? getUniqueActions(getMockOperationLogs(user) || []) : [];

  const handleExportLogs = () => {
    toast.success('正在导出操作日志...');
    console.log('导出日志:', filteredData);
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case '创建活码':
      case '批量添加':
      case '创建用户':
        return <Plus className="h-4 w-4 text-green-600" />;
      case '编辑活码':
      case '重置密码':
      case '修改权限':
        return <Edit className="h-4 w-4 text-blue-600" />;
      case '删除用户':
      case '删除活码':
        return <Trash2 className="h-4 w-4 text-red-600" />;
      case '登录失败':
        return <Shield className="h-4 w-4 text-red-600" />;
      case '域名过期':
        return <Clock className="h-4 w-4 text-orange-600" />;
      case '访问记录':
        return <Eye className="h-4 w-4 text-blue-600" />;
      default:
        return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'super-admin':
        return <Shield className="h-3 w-3 text-red-600" />;
      case 'boss':
        return <Shield className="h-3 w-3 text-blue-600" />;
      case 'manager':
        return <User className="h-3 w-3 text-green-600" />;
      default:
        return <User className="h-3 w-3" />;
    }
  };

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl">操作日志</h1>
          <p className="text-muted-foreground">
            查看系统操作记录和审计日志
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleExportLogs}>
            <Download className="mr-2 h-4 w-4" />
            导出日志
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总记录数</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">操作记录总数</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">今日操作</CardTitle>
            <Calendar className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.today}</div>
            <p className="text-xs text-muted-foreground">今天的操作数量</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">成功操作</CardTitle>
            <Plus className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.success}</div>
            <p className="text-xs text-muted-foreground">成功执行的操作</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">失败操作</CardTitle>
            <Trash2 className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.failed}</div>
            <p className="text-xs text-muted-foreground">执行失败的操作</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">警告事件</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.warning}</div>
            <p className="text-xs text-muted-foreground">需要关注的警告</p>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和过滤 - 优化的网格布局 */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* 搜索框 */}
            <div className="lg:col-span-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索操作日志..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            {/* 动作过滤 */}
            <div>
              <Select value={actionFilter} onValueChange={setActionFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="选择动作" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部动作</SelectItem>
                  {(uniqueActions || []).map(action => (
                    <SelectItem key={action} value={action}>{action}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 用户角色过滤 */}
            <div>
              <Select value={userFilter} onValueChange={setUserFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="选择角色" />
                </SelectTrigger>
                <SelectContent>
                  {(ROLE_FILTER_OPTIONS || fallbackRoleFilterOptions).map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 时间过滤 */}
            <div>
              <Select value={timeFilter} onValueChange={setTimeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="选择时间" />
                </SelectTrigger>
                <SelectContent>
                  {(TIME_FILTER_OPTIONS || fallbackTimeFilterOptions).map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 操作日志列表 */}
      <Card>
        <CardHeader>
          <CardTitle>操作日志 (共 {totalItems} 条记录)</CardTitle>
          <CardDescription>系统操作的详细记录</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* 分页控件 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Label className="text-sm">每页显示:</Label>
                <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemsPerPage(Number(value))}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
                <span className="text-sm text-muted-foreground">
                  共 {totalItems} 条
                </span>
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToFirstPage}
                  disabled={currentPage === 1}
                >
                  <ChevronsLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPrevPage}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="text-sm text-muted-foreground">
                  第 {currentPage} 页，共 {totalPages} 页
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNextPage}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToLastPage}
                  disabled={currentPage === totalPages}
                >
                  <ChevronsRight className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* 数据表格 */}
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>时间</TableHead>
                  <TableHead>用户</TableHead>
                  <TableHead>操作</TableHead>
                  <TableHead>目标</TableHead>
                  <TableHead>详情</TableHead>
                  <TableHead>IP地址</TableHead>
                  <TableHead>状态</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentPageData?.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-3 w-3 text-muted-foreground" />
                        <span className="text-sm">{log.createdAt}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {getRoleIcon(log.userRole)}
                        <div>
                          <div className="font-medium">{log.userName}</div>
                          <div className="text-xs text-muted-foreground">
                            {(ROLE_LABELS || fallbackRoleLabels)[log.userRole] || log.userRole}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {getActionIcon(log.action)}
                        <span>{log.action}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{log.targetName}</div>
                        <div className="text-xs text-muted-foreground">{log.targetType}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">{log.details}</span>
                    </TableCell>
                    <TableCell>
                      <span className="font-mono text-sm">{log.ipAddress}</span>
                    </TableCell>
                    <TableCell>
                      <Badge variant={
                        log.status === 'success' ? 'default' : 
                        log.status === 'failed' ? 'destructive' : 'secondary'
                      }>
                        {log.status === 'success' ? '成功' : 
                         log.status === 'failed' ? '失败' : '警告'}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {/* 底部分页信息 */}
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <div>
                显示第 {startIndex + 1} 到 {Math.min(endIndex, totalItems)} 条，共 {totalItems} 条数据
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPrevPage}
                  disabled={currentPage === 1}
                >
                  上一页
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNextPage}
                  disabled={currentPage === totalPages}
                >
                  下一页
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}