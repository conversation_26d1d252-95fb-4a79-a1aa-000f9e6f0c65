export interface User {
  id: string;
  name: string;
  email: string;
  role: 'super-admin' | 'boss' | 'manager';
}

export interface SystemUser extends User {
  status: 'active' | 'disabled';
  createdAt: string;
  lastLogin?: string;
  superiorId?: string;
  superiorName?: string;
  subordinateCount: number;
}

export interface ShortCode {
  id: string;
  title: string;
  shortCode: string;
  type: 'WhatsApp' | 'Telegram' | '网址';
  visits: number;
  pollingCount?: number;
  status: 'normal' | 'disabled';
  createdAt: string;
  creatorId: string;
  creatorName: string;
  description?: string;
}

export interface LinkBatch {
  id: string;
  name: string;
  createdAt: string;
  priority: number;
  priorityCount: number;
  usedCount: number;
  status: 'priority' | 'merged' | 'normal';
  linkType: 'WhatsApp' | 'Telegram' | '网址';
  shortCodeId: string;
}

export interface LinkData {
  id: string;
  type: 'WhatsApp' | 'Telegram' | '网址';
  content: string;
  addedAt: string;
  batchId: string;
  batchName: string;
  usageCount: number;
  status: 'active' | 'disabled';
  priority: number;
  shortCodeId: string;
}

export interface AccessRecord {
  id: string;
  shortCode: string;
  shortCodeTitle: string;
  ip: string;
  location: string;
  userAgent: string;
  deviceType: 'desktop' | 'mobile' | 'tablet';
  referer: string;
  visitTime: string;
  creatorId: string;
  creatorName: string;
}

export interface OperationLog {
  id: string;
  userId: string;
  userName: string;
  userRole: 'super-admin' | 'boss' | 'manager';
  action: string;
  targetType: string;
  targetName: string;
  details: string;
  ipAddress: string;
  createdAt: string;
  status: 'success' | 'failed' | 'warning';
}

export type PageType = 'dashboard' | 'short-codes' | 'access-records' | 'user-management' | 'domain-management' | 'link-polling' | 'operation-logs';

export type ViewMode = 'my' | 'subordinates';

export interface ComponentProps {
  user: User;
  onNavigate?: (page: string) => void;
  onNavigateToAccess?: (shortCodeId: string) => void;
  onNavigateToPolling?: (shortCodeId: string) => void;
}