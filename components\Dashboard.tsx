import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Link, 
  Activity, 
  Globe,
  Plus,
  Eye,
  BarChart3,
  RotateCcw,
  User
} from 'lucide-react';
import { toast } from 'sonner@2.0.3';

interface User {
  id: string;
  name: string;
  email: string;
  role: 'super-admin' | 'boss' | 'manager';
}

interface DashboardProps {
  user: User;
  onNavigate: (page: string) => void;
}

export function Dashboard({ user, onNavigate }: DashboardProps) {
  // 模拟数据
  const stats = {
    'super-admin': {
      totalShortCodes: 1248,
      totalVisits: 45678,
      totalUsers: 156,
      activeDomains: 23,
      todayVisits: 2341,
      todayNewCodes: 45
    },
    'boss': {
      totalShortCodes: 456,
      totalVisits: 12345,
      totalUsers: 8,
      activeDomains: 23,
      todayVisits: 789,
      todayNewCodes: 12
    },
    'manager': {
      totalShortCodes: 89,
      totalVisits: 3456,
      totalUsers: 1,
      activeDomains: 23,
      todayVisits: 156,
      todayNewCodes: 3
    }
  };

  const currentStats = stats[user.role];

  const recentActivities = [
    { id: 1, action: '创建活码', target: 'WS推广链接001', time: '2分钟前', type: 'create' },
    { id: 2, action: '访问记录', target: '来自广东深圳的访问', time: '5分钟前', type: 'visit' },
    { id: 3, action: '用户登录', target: '李经理', time: '10分钟前', type: 'login' },
    { id: 4, action: '编辑活码', target: 'TG客服链接003', time: '15分钟前', type: 'edit' },
    { id: 5, action: '域名过期提醒', target: 'example1.com', time: '1小时前', type: 'warning' }
  ];

  // 热门短码数据，包含创建者信息
  const topShortCodes = [
    { id: 1, title: 'WS客服主链接', visits: 1234, code: 'rJgMftSC', creator: { name: '张总', role: 'boss' } },
    { id: 2, title: 'TG推广渠道A', visits: 987, code: 'kL9mNpQr', creator: { name: '李经理', role: 'manager' } },
    { id: 3, title: '官网导流', visits: 756, code: 'bV3xZcFg', creator: { name: '超级管理员', role: 'super-admin' } },
    { id: 4, title: '活动页面', visits: 543, code: 'hY8nBmKl', creator: { name: '王经理', role: 'manager' } },
    { id: 5, title: '客服咨询', visits: 321, code: 'pQ2wErTy', creator: { name: '张总', role: 'boss' } }
  ];

  const handleCreateShortCode = () => {
    toast.success('正在跳转到短码管理...');
    onNavigate('short-codes');
  };

  const handleViewAccessRecords = () => {
    toast.success('正在跳转到访问记录...');
    onNavigate('access-records');
  };

  const handleUserManagement = () => {
    if (user.role === 'manager') {
      toast.error('权限不足，无法访问用户管理');
      return;
    }
    toast.success('正在跳转到用户管理...');
    onNavigate('user-management');
  };

  const handleDataStatistics = () => {
    toast.success('正在跳转到访问记录统计...');
    onNavigate('access-records');
  };

  const handleLinkPolling = () => {
    toast.success('正在跳转到链接轮询...');
    onNavigate('link-polling');
  };

  // 判断是否显示创建者信息
  const shouldShowCreator = user.role === 'super-admin' || user.role === 'boss';

  return (
    <div className="flex-1 space-y-8 p-6 bg-background/95">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl tracking-tight">工作台</h1>
          <p className="text-muted-foreground text-base">
            欢迎回来，{user.name}
          </p>
        </div>
        <div className="flex space-x-3">
          <Button 
            onClick={handleCreateShortCode}
            className="shadow-sm hover:shadow-md transition-shadow duration-200"
          >
            <Plus className="mr-2 h-4 w-4" />
            创建活码
          </Button>
          <Button 
            variant="outline" 
            onClick={handleLinkPolling}
            className="shadow-sm hover:shadow-md transition-shadow duration-200"
          >
            <RotateCcw className="mr-2 h-4 w-4" />
            链接轮询
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-0 shadow-md hover:shadow-lg transition-shadow duration-300 bg-gradient-to-br from-white to-slate-50/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              短码总数
            </CardTitle>
            <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
              <Link className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent className="pt-1">
            <div className="text-2xl font-bold tracking-tight mb-1">{currentStats.totalShortCodes.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground flex items-center">
              <span className="text-emerald-600 inline-flex items-center bg-emerald-50 px-2 py-0.5 rounded-full mr-2">
                <TrendingUp className="mr-1 h-3 w-3" />
                +{currentStats.todayNewCodes}
              </span>
              今日新增
            </p>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-md hover:shadow-lg transition-shadow duration-300 bg-gradient-to-br from-white to-emerald-50/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              总访问量
            </CardTitle>
            <div className="h-8 w-8 rounded-full bg-emerald-100 flex items-center justify-center">
              <Activity className="h-4 w-4 text-emerald-600" />
            </div>
          </CardHeader>
          <CardContent className="pt-1">
            <div className="text-2xl font-bold tracking-tight mb-1">{currentStats.totalVisits.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground flex items-center">
              <span className="text-emerald-600 inline-flex items-center bg-emerald-50 px-2 py-0.5 rounded-full mr-2">
                <TrendingUp className="mr-1 h-3 w-3" />
                +{currentStats.todayVisits}
              </span>
              今日访问
            </p>
          </CardContent>
        </Card>

        {user.role !== 'manager' && (
          <Card className="border-0 shadow-md hover:shadow-lg transition-shadow duration-300 bg-gradient-to-br from-white to-purple-50/50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                用户数量
              </CardTitle>
              <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center">
                <Users className="h-4 w-4 text-purple-600" />
              </div>
            </CardHeader>
            <CardContent className="pt-1">
              <div className="text-2xl font-bold tracking-tight mb-1">{currentStats.totalUsers}</div>
              <p className="text-xs text-muted-foreground">
                {user.role === 'super-admin' ? '全系统用户' : '下属经理'}
              </p>
            </CardContent>
          </Card>
        )}

        <Card className="border-0 shadow-md hover:shadow-lg transition-shadow duration-300 bg-gradient-to-br from-white to-amber-50/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              可用域名
            </CardTitle>
            <div className="h-8 w-8 rounded-full bg-amber-100 flex items-center justify-center">
              <Globe className="h-4 w-4 text-amber-600" />
            </div>
          </CardHeader>
          <CardContent className="pt-1">
            <div className="text-2xl font-bold tracking-tight mb-1">{currentStats.activeDomains}</div>
            <p className="text-xs text-muted-foreground">
              5天内到期：<span className="text-amber-600 font-medium">3个</span>
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
        {/* 最近活动 */}
        <Card className="col-span-4 border-0 shadow-md">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center">
              <div className="h-2 w-2 rounded-full bg-blue-500 mr-2"></div>
              最近活动
            </CardTitle>
            <CardDescription>
              系统最新的操作记录
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity, index) => (
                <div key={activity.id} className={`flex items-center p-3 rounded-lg hover:bg-muted/30 transition-colors ${index !== recentActivities.length - 1 ? 'border-b border-border/30' : ''}`}>
                  <div className="flex-1 space-y-1">
                    <p className="text-sm font-medium leading-none">
                      <span className="text-muted-foreground">{activity.action}：</span>
                      <span className="text-foreground">{activity.target}</span>
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {activity.time}
                    </p>
                  </div>
                  <Badge 
                    variant={
                      activity.type === 'warning' ? 'destructive' :
                      activity.type === 'create' ? 'default' : 'secondary'
                    }
                    className="ml-3 shadow-sm"
                  >
                    {activity.type === 'create' ? '新建' :
                     activity.type === 'visit' ? '访问' :
                     activity.type === 'edit' ? '编辑' :
                     activity.type === 'login' ? '登录' : '警告'}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 热门短码 */}
        <Card className="col-span-3 border-0 shadow-md">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center">
              <div className="h-2 w-2 rounded-full bg-emerald-500 mr-2"></div>
              热门短码
            </CardTitle>
            <CardDescription>
              访问量最高的短码
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {topShortCodes.map((code, index) => (
                <div key={code.id} className="flex items-center p-3 rounded-lg hover:bg-muted/30 transition-colors border border-border/20">
                  <div className={`h-8 w-8 rounded-full flex items-center justify-center text-xs font-bold mr-3 ${
                    index === 0 ? 'bg-yellow-100 text-yellow-800' :
                    index === 1 ? 'bg-gray-100 text-gray-800' :
                    index === 2 ? 'bg-orange-100 text-orange-800' :
                    'bg-muted text-muted-foreground'
                  }`}>
                    #{index + 1}
                  </div>
                  <div className="flex-1 space-y-1 min-w-0">
                    <p className="text-sm font-medium leading-none truncate">
                      {code.title}
                    </p>
                    <div className="flex items-center space-x-2">
                      <p className="text-xs text-muted-foreground font-mono">
                        /{code.code}
                      </p>
                      {shouldShowCreator && (
                        <div className="flex items-center text-xs text-muted-foreground">
                          <User className="h-3 w-3 mr-1" />
                          <span className="truncate">{code.creator.name}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  <Badge variant="outline" className="ml-2 shadow-sm">
                    <Eye className="mr-1 h-3 w-3" />
                    {code.visits.toLocaleString()}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 快速操作 */}
      <Card className="border-0 shadow-md">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center">
            <div className="h-2 w-2 rounded-full bg-purple-500 mr-2"></div>
            快速操作
          </CardTitle>
          <CardDescription>
            常用功能快捷入口
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
            <Button 
              variant="outline" 
              className="h-24 flex-col border-2 border-dashed hover:border-solid hover:border-primary/20 hover:bg-primary/5 transition-all duration-200 group"
              onClick={handleCreateShortCode}
            >
              <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center mb-2 group-hover:bg-blue-200 transition-colors">
                <Plus className="h-5 w-5 text-blue-600" />
              </div>
              <span className="font-medium">创建新活码</span>
            </Button>
            
            <Button 
              variant="outline" 
              className="h-24 flex-col border-2 border-dashed hover:border-solid hover:border-primary/20 hover:bg-primary/5 transition-all duration-200 group"
              onClick={handleViewAccessRecords}
            >
              <div className="h-10 w-10 rounded-full bg-emerald-100 flex items-center justify-center mb-2 group-hover:bg-emerald-200 transition-colors">
                <Activity className="h-5 w-5 text-emerald-600" />
              </div>
              <span className="font-medium">查看访问记录</span>
            </Button>
            
            {user.role !== 'manager' && (
              <Button 
                variant="outline" 
                className="h-24 flex-col border-2 border-dashed hover:border-solid hover:border-primary/20 hover:bg-primary/5 transition-all duration-200 group"
                onClick={handleUserManagement}
              >
                <div className="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center mb-2 group-hover:bg-purple-200 transition-colors">
                  <Users className="h-5 w-5 text-purple-600" />
                </div>
                <span className="font-medium">用户管理</span>
              </Button>
            )}
            
            <Button 
              variant="outline" 
              className="h-24 flex-col border-2 border-dashed hover:border-solid hover:border-primary/20 hover:bg-primary/5 transition-all duration-200 group"
              onClick={handleDataStatistics}
            >
              <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center mb-2 group-hover:bg-indigo-200 transition-colors">
                <BarChart3 className="h-5 w-5 text-indigo-600" />
              </div>
              <span className="font-medium">数据统计</span>
            </Button>
            
            <Button 
              variant="outline" 
              className="h-24 flex-col border-2 border-dashed hover:border-solid hover:border-primary/20 hover:bg-primary/5 transition-all duration-200 group"
              onClick={handleLinkPolling}
            >
              <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center mb-2 group-hover:bg-amber-200 transition-colors">
                <RotateCcw className="h-5 w-5 text-amber-600" />
              </div>
              <span className="font-medium">链接轮询</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}