import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from './ui/dropdown-menu';
import { 
  Search, 
  MoreHorizontal, 
  Edit, 
  UserPlus, 
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  Filter,
  Eye
} from 'lucide-react';

interface User {
  id: string;
  name: string;
  email: string;
  role: 'super-admin' | 'boss' | 'manager';
}

interface CustomerResource {
  id: string;
  customerName: string;
  customerType: string;
  status: 'pending' | 'following' | 'completed' | 'closed';
  currentManager: string;
  currentManagerId: string;
  assignedDate: string;
  lastFollowUp: string;
  source: string;
  value: number;
  priority: 'high' | 'medium' | 'low';
  remark?: string;
}

interface CustomerResourcesProps {
  user: User;
}

export function CustomerResources({ user }: CustomerResourcesProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false);

  const statusLabels = {
    'pending': '待跟进',
    'following': '跟进中',
    'completed': '已完成',
    'closed': '已关闭'
  };

  const statusColors = {
    'pending': 'secondary',
    'following': 'default',
    'completed': 'default',
    'closed': 'secondary'
  } as const;

  const priorityLabels = {
    'high': '高',
    'medium': '中',
    'low': '低'
  };

  const priorityColors = {
    'high': 'destructive',
    'medium': 'default',
    'low': 'secondary'
  } as const;

  // 模拟数据
  const customerResources: CustomerResource[] = [
    {
      id: '1',
      customerName: '华为科技有限公司',
      customerType: '企业客户',
      status: 'following',
      currentManager: user.role === 'manager' ? user.name : '李经理',
      currentManagerId: '3',
      assignedDate: '2024-01-10 09:30:00',
      lastFollowUp: '2024-01-14 15:20:00',
      source: '线上推广',
      value: 500000,
      priority: 'high',
      remark: '重要客户，优先跟进'
    },
    {
      id: '2',
      customerName: '腾讯云服务',
      customerType: '企业客户',
      status: 'pending',
      currentManager: user.role === 'manager' ? user.name : '王经理',
      currentManagerId: '4',
      assignedDate: '2024-01-12 14:15:00',
      lastFollowUp: '2024-01-12 14:15:00',
      source: '客户推荐',
      value: 300000,
      priority: 'medium'
    },
    {
      id: '3',
      customerName: '小米科技',
      customerType: '企业客户',
      status: 'completed',
      currentManager: user.role === 'manager' ? user.name : '李经理',
      currentManagerId: '3',
      assignedDate: '2024-01-05 11:00:00',
      lastFollowUp: '2024-01-13 16:30:00',
      source: '展会获客',
      value: 200000,
      priority: 'low'
    }
  ];

  const filteredResources = customerResources.filter(resource => {
    const matchesSearch = resource.customerName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || resource.status === selectedStatus;
    
    // 根据用户角色过滤数据
    if (user.role === 'manager') {
      return matchesSearch && matchesStatus && resource.currentManager === user.name;
    }
    
    return matchesSearch && matchesStatus;
  });

  const stats = {
    total: filteredResources.length,
    pending: filteredResources.filter(r => r.status === 'pending').length,
    following: filteredResources.filter(r => r.status === 'following').length,
    completed: filteredResources.filter(r => r.status === 'completed').length
  };

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl">客户资源</h1>
          <p className="text-muted-foreground">
            管理客户资源分配和跟进记录
          </p>
        </div>
        {user.role !== 'manager' && (
          <Dialog open={isAssignDialogOpen} onOpenChange={setIsAssignDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <UserPlus className="mr-2 h-4 w-4" />
                分配客户
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>分配客户资源</DialogTitle>
                <DialogDescription>
                  将客户资源分配给指定经理
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label>目标经理</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="选择经理" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="manager1">李经理</SelectItem>
                      <SelectItem value="manager2">王经理</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>分配说明</Label>
                  <Textarea placeholder="选填" />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAssignDialogOpen(false)}>
                  取消
                </Button>
                <Button>确定分配</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">客户总数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">待跟进</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.pending}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">跟进中</CardTitle>
            <AlertCircle className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.following}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已完成</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索客户名称..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-40">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="pending">待跟进</SelectItem>
                <SelectItem value="following">跟进中</SelectItem>
                <SelectItem value="completed">已完成</SelectItem>
                <SelectItem value="closed">已关闭</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 客户列表 */}
      <Card>
        <CardHeader>
          <CardTitle>客户资源列表</CardTitle>
          <CardDescription>
            共 {filteredResources.length} 个客户
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>客户名称</TableHead>
                <TableHead>类型</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>当前经理</TableHead>
                <TableHead>优先级</TableHead>
                <TableHead>客户价值</TableHead>
                <TableHead>最后跟进</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredResources.map((resource) => (
                <TableRow key={resource.id}>
                  <TableCell className="font-medium">{resource.customerName}</TableCell>
                  <TableCell>{resource.customerType}</TableCell>
                  <TableCell>
                    <Badge variant={statusColors[resource.status]}>
                      {statusLabels[resource.status]}
                    </Badge>
                  </TableCell>
                  <TableCell>{resource.currentManager}</TableCell>
                  <TableCell>
                    <Badge variant={priorityColors[resource.priority]}>
                      {priorityLabels[resource.priority]}
                    </Badge>
                  </TableCell>
                  <TableCell>¥{resource.value.toLocaleString()}</TableCell>
                  <TableCell>{resource.lastFollowUp}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Eye className="mr-2 h-4 w-4" />
                          查看详情
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="mr-2 h-4 w-4" />
                          编辑信息
                        </DropdownMenuItem>
                        {user.role !== 'manager' && (
                          <DropdownMenuItem>
                            <UserPlus className="mr-2 h-4 w-4" />
                            重新分配
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}