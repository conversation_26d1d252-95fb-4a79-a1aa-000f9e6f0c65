import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Label } from './ui/label';
import { 
  Search, 
  Eye, 
  TrendingUp, 
  MapPin,
  Smartphone,
  Calendar,
  Filter,
  Download,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react';
import { toast } from 'sonner@2.0.3';

interface User {
  id: string;
  name: string;
  role: 'super-admin' | 'boss' | 'manager';
}

interface AccessRecord {
  id: string;
  shortCode: string;
  shortCodeTitle: string;
  ip: string;
  location: string;
  userAgent: string;
  deviceType: 'desktop' | 'mobile' | 'tablet';
  referer: string;
  visitTime: string;
  creatorId: string;
  creatorName: string;
}

interface AccessRecordsProps {
  user: User;
  selectedShortCodeId?: string | null;
}

export function AccessRecords({ user, selectedShortCodeId }: AccessRecordsProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [timeFilter, setTimeFilter] = useState('all');
  const [deviceFilter, setDeviceFilter] = useState('all');
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  // 如果传入了selectedShortCodeId，则在搜索框中预设对应的短码
  useEffect(() => {
    if (selectedShortCodeId) {
      // 根据shortCodeId找到对应的短码
      const targetRecord = accessRecords.find(record => record.id === selectedShortCodeId);
      if (targetRecord) {
        setSearchTerm(targetRecord.shortCode);
        toast.success(`已筛选到短码 /${targetRecord.shortCode} 的访问记录`);
      }
    }
  }, [selectedShortCodeId]);

  // 模拟数据 - 增加更多数据用于测试分页
  const accessRecords: AccessRecord[] = [
    {
      id: '1',
      shortCode: 'rJgMftSC',
      shortCodeTitle: 'WS客服主链接',
      ip: '************',
      location: '广东深圳',
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)',
      deviceType: 'mobile',
      referer: 'https://www.google.com',
      visitTime: '2024-01-15 14:22:30',
      creatorId: user.id,
      creatorName: user.name
    },
    {
      id: '2',
      shortCode: 'kL9mNpQr',
      shortCodeTitle: 'TG推广渠道A',
      ip: '*************',
      location: '北京朝阳',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      deviceType: 'desktop',
      referer: 'https://t.me/channel',
      visitTime: '2024-01-15 13:45:15',
      creatorId: user.id,
      creatorName: user.name
    },
    {
      id: '3',
      shortCode: 'bV3xZcFg',
      shortCodeTitle: '官网导流',
      ip: '************',
      location: '上海浦东',
      userAgent: 'Mozilla/5.0 (iPad; CPU OS 15_0 like Mac OS X)',
      deviceType: 'tablet',
      referer: 'https://weibo.com',
      visitTime: '2024-01-15 12:30:45',
      creatorId: user.id,
      creatorName: user.name
    },
    {
      id: '4',
      shortCode: 'hY8nBmKl',
      shortCodeTitle: '活动页面',
      ip: '************',
      location: '江苏南京',
      userAgent: 'Mozilla/5.0 (Android 12; Mobile)',
      deviceType: 'mobile',
      referer: 'https://facebook.com',
      visitTime: '2024-01-15 11:15:20',
      creatorId: user.id,
      creatorName: user.name
    },
    {
      id: '5',
      shortCode: 'pQ2wErTy',
      shortCodeTitle: '客服咨询',
      ip: '************',
      location: '四川成都',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
      deviceType: 'desktop',
      referer: 'direct',
      visitTime: '2024-01-15 10:05:10',
      creatorId: user.id,
      creatorName: user.name
    },
    // 增加更多测试数据
    ...Array.from({ length: 50 }, (_, i) => ({
      id: `test-${i + 6}`,
      shortCode: i % 3 === 0 ? 'rJgMftSC' : i % 3 === 1 ? 'kL9mNpQr' : 'bV3xZcFg',
      shortCodeTitle: i % 3 === 0 ? 'WS客服主链接' : i % 3 === 1 ? 'TG推广渠道A' : '官网导流',
      ip: `192.168.${Math.floor(i / 10) + 1}.${(i % 10) + 1}`,
      location: ['广东深圳', '北京朝阳', '上海浦东', '江苏南京', '四川成都'][i % 5],
      userAgent: 'Mozilla/5.0 (compatible test agent)',
      deviceType: ['mobile', 'desktop', 'tablet'][i % 3] as 'mobile' | 'desktop' | 'tablet',
      referer: ['https://www.google.com', 'https://weibo.com', 'direct', 'https://facebook.com'][i % 4],
      visitTime: `2024-01-${10 + (i % 20)} ${10 + (i % 14)}:${10 + (i % 50)}:${10 + (i % 50)}`,
      creatorId: user.id,
      creatorName: user.name
    }))
  ];

  // 根据用户权限过滤数据
  const getFilteredRecords = () => {
    let filteredRecords = accessRecords;

    // 根据角色过滤
    if (user.role === 'manager') {
      filteredRecords = accessRecords.filter(record => record.creatorId === user.id);
    }

    // 根据短码搜索（移除了IP和地理位置搜索）
    if (searchTerm) {
      filteredRecords = filteredRecords.filter(record =>
        record.shortCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.shortCodeTitle.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // 时间过滤
    if (timeFilter !== 'all') {
      const now = new Date();
      const filterDate = new Date();
      
      switch (timeFilter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          break;
      }
      
      filteredRecords = filteredRecords.filter(record => 
        new Date(record.visitTime) >= filterDate
      );
    }

    // 设备类型过滤
    if (deviceFilter !== 'all') {
      filteredRecords = filteredRecords.filter(record => record.deviceType === deviceFilter);
    }

    return filteredRecords.sort((a, b) => new Date(b.visitTime).getTime() - new Date(a.visitTime).getTime());
  };

  const filteredData = getFilteredRecords();

  // 分页计算
  const totalItems = filteredData.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentPageData = filteredData.slice(startIndex, endIndex);

  // 重置分页当过滤条件改变时
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, timeFilter, deviceFilter, itemsPerPage]);

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'mobile':
        return <Smartphone className="h-4 w-4 text-blue-600" />;
      case 'tablet':
        return <Smartphone className="h-4 w-4 text-green-600" />;
      case 'desktop':
        return <Smartphone className="h-4 w-4 text-purple-600" />;
      default:
        return <Smartphone className="h-4 w-4" />;
    }
  };

  // 分页控制函数
  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  const goToFirstPage = () => goToPage(1);
  const goToLastPage = () => goToPage(totalPages);
  const goToPrevPage = () => goToPage(currentPage - 1);
  const goToNextPage = () => goToPage(currentPage + 1);

  // 统计数据
  const stats = {
    totalVisits: filteredData.length,
    uniqueVisitors: new Set(filteredData.map(record => record.ip)).size,
    mobileVisits: filteredData.filter(record => record.deviceType === 'mobile').length,
    desktopVisits: filteredData.filter(record => record.deviceType === 'desktop').length,
    tabletVisits: filteredData.filter(record => record.deviceType === 'tablet').length
  };

  const mobilePercentage = stats.totalVisits > 0 ? Math.round((stats.mobileVisits / stats.totalVisits) * 100) : 0;
  const desktopPercentage = stats.totalVisits > 0 ? Math.round((stats.desktopVisits / stats.totalVisits) * 100) : 0;

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl">访问记录</h1>
          <p className="text-muted-foreground">
            查看活码的访问统计和详细记录
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出数据
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总访问量</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalVisits}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 inline-flex items-center">
                <TrendingUp className="mr-1 h-3 w-3" />
                +12%
              </span>
              比昨天
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">独立访客</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.uniqueVisitors}</div>
            <p className="text-xs text-muted-foreground">
              基于IP地址统计
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">设备分布</CardTitle>
            <Smartphone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>移动端</span>
                <span className="font-medium">{mobilePercentage}%</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>桌面端</span>
                <span className="font-medium">{desktopPercentage}%</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和过滤 - 优化的网格布局 */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* 搜索框 */}
            <div className="lg:col-span-2">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索短码或活码标题..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            {/* 时间过滤 */}
            <div>
              <Select value={timeFilter} onValueChange={setTimeFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部时间</SelectItem>
                  <SelectItem value="today">今天</SelectItem>
                  <SelectItem value="week">最近7天</SelectItem>
                  <SelectItem value="month">最近30天</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 设备过滤 */}
            <div>
              <Select value={deviceFilter} onValueChange={setDeviceFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部设备</SelectItem>
                  <SelectItem value="mobile">移动设备</SelectItem>
                  <SelectItem value="desktop">桌面设备</SelectItem>
                  <SelectItem value="tablet">平板设备</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 访问记录列表 */}
      <Card>
        <CardHeader>
          <CardTitle>访问记录 (共 {totalItems} 条)</CardTitle>
          <CardDescription>
            活码的详细访问记录和来源信息
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* 分页控件 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Label className="text-sm">每页显示:</Label>
                <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemsPerPage(Number(value))}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
                <span className="text-sm text-muted-foreground">
                  共 {totalItems} 条
                </span>
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToFirstPage}
                  disabled={currentPage === 1}
                >
                  <ChevronsLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPrevPage}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="text-sm text-muted-foreground">
                  第 {currentPage} 页，共 {totalPages} 页
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNextPage}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToLastPage}
                  disabled={currentPage === totalPages}
                >
                  <ChevronsRight className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* 数据表格 */}
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>短码</TableHead>
                  <TableHead>活码标题</TableHead>
                  <TableHead>访问IP</TableHead>
                  <TableHead>地理位置</TableHead>
                  <TableHead>设备类型</TableHead>
                  <TableHead>来源</TableHead>
                  <TableHead>访问时间</TableHead>
                  {user.role !== 'manager' && <TableHead>创建者</TableHead>}
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentPageData.map((record) => (
                  <TableRow key={record.id}>
                    <TableCell>
                      <span className="font-mono text-sm bg-muted px-2 py-1 rounded">
                        /{record.shortCode}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span className="font-medium">{record.shortCodeTitle}</span>
                    </TableCell>
                    <TableCell>
                      <span className="font-mono text-sm">{record.ip}</span>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <MapPin className="h-3 w-3 text-muted-foreground" />
                        <span className="text-sm">{record.location}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {getDeviceIcon(record.deviceType)}
                        <Badge variant="outline" className="text-xs">
                          {record.deviceType === 'mobile' ? '移动端' :
                           record.deviceType === 'desktop' ? '桌面端' : '平板端'}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-muted-foreground">
                        {record.referer === 'direct' ? '直接访问' : 
                         record.referer.includes('google') ? 'Google' :
                         record.referer.includes('weibo') ? '微博' :
                         record.referer.includes('facebook') ? 'Facebook' :
                         record.referer.includes('t.me') ? 'Telegram' : '其他'}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-3 w-3 text-muted-foreground" />
                        <span className="text-sm">{record.visitTime}</span>
                      </div>
                    </TableCell>
                    {user.role !== 'manager' && (
                      <TableCell>
                        <span className="text-sm">{record.creatorName}</span>
                      </TableCell>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {/* 底部分页信息 */}
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <div>
                显示第 {startIndex + 1} 到 {Math.min(endIndex, totalItems)} 条，共 {totalItems} 条数据
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPrevPage}
                  disabled={currentPage === 1}
                >
                  上一页
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNextPage}
                  disabled={currentPage === totalPages}
                >
                  下一页
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}