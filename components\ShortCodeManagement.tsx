import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from './ui/alert-dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from './ui/dropdown-menu';
import { 
  Plus, 
  Search, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  RefreshCw,
  Eye,
  Link as LinkIcon,
  TrendingUp,
  Users as UsersIcon,
  RotateCcw,
  MessageCircle,
  Send,
  Globe,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react';
import { toast } from 'sonner@2.0.3';

interface User {
  id: string;
  name: string;
  role: 'super-admin' | 'boss' | 'manager';
}

interface ShortCode {
  id: string;
  title: string;
  shortCode: string;
  type: 'WhatsApp' | 'Telegram' | '网址';
  visits: number;
  pollingCount: number; // 轮询数据数量
  status: 'normal' | 'disabled'; // 根据轮询数据自动判断
  createdAt: string;
  creatorId: string;
  creatorName: string;
  description?: string;
}

interface ShortCodeManagementProps {
  user: User;
  onNavigateToAccess?: (shortCodeId: string) => void;
  onNavigateToPolling?: (shortCodeId: string) => void;
}

export function ShortCodeManagement({ user, onNavigateToAccess, onNavigateToPolling }: ShortCodeManagementProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingCode, setEditingCode] = useState<ShortCode | null>(null);
  const [viewMode, setViewMode] = useState<'my' | 'subordinates'>('my');
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const [newShortCode, setNewShortCode] = useState({
    title: '',
    type: 'WhatsApp' as const,
    description: '',
    randomCode: ''
  });

  // 生成随机码函数
  const generateRandomCode = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  // 在对话框打开时生成一次随机码
  useEffect(() => {
    if (isCreateDialogOpen && !newShortCode.randomCode) {
      setNewShortCode(prev => ({
        ...prev,
        randomCode: generateRandomCode()
      }));
    }
  }, [isCreateDialogOpen]);

  // 根据类型获取目标URL（不显示，仅用于后端逻辑）
  const getTargetUrl = (type: 'WhatsApp' | 'Telegram' | '网址') => {
    switch (type) {
      case 'WhatsApp':
        return 'https://api.whatsapp.com/send/?phone=';
      case 'Telegram':
        return 'https://t.me/';
      case '网址':
        return 'https://';
      default:
        return 'https://';
    }
  };

  // 模拟轮询数据，用于判断状态
  const mockPollingData = [
    { shortCodeId: '1', count: 5 },
    { shortCodeId: '2', count: 8 },
    { shortCodeId: '3', count: 0 }, // 无轮询数据
    { shortCodeId: '4', count: 12 }
  ];

  // 获取轮询数据数量
  const getPollingCount = (shortCodeId: string) => {
    const polling = mockPollingData.find(p => p.shortCodeId === shortCodeId);
    return polling ? polling.count : 0;
  };

  // 判断活码状态（根据是否有轮询数据）
  const getShortCodeStatus = (shortCodeId: string): 'normal' | 'disabled' => {
    const pollingCount = getPollingCount(shortCodeId);
    return pollingCount > 0 ? 'normal' : 'disabled';
  };

  // 模拟数据 - 根据用户角色显示不同数据
  const getAllShortCodes = (): ShortCode[] => [
    {
      id: '1',
      title: 'WS客服主链接',
      shortCode: 'rJgMftSC',
      type: 'WhatsApp',
      visits: 1234,
      pollingCount: getPollingCount('1'),
      status: getShortCodeStatus('1'),
      createdAt: '2024-01-15 10:30:00',
      creatorId: user.id,
      creatorName: user.name,
      description: '主要客服联系方式'
    },
    {
      id: '2',
      title: 'TG推广渠道A',
      shortCode: 'kL9mNpQr',
      type: 'Telegram',
      visits: 987,
      pollingCount: getPollingCount('2'),
      status: getShortCodeStatus('2'),
      createdAt: '2024-01-14 15:20:00',
      creatorId: user.id,
      creatorName: user.name,
      description: '推广渠道A'
    },
    {
      id: '3',
      title: '官网导流页面',
      shortCode: 'bV3cDeF5',
      type: '网址',
      visits: 456,
      pollingCount: getPollingCount('3'),
      status: getShortCodeStatus('3'),
      createdAt: '2024-01-13 09:15:00',
      creatorId: user.id,
      creatorName: user.name,
      description: '官方网站导流'
    },
    {
      id: '4',
      title: '下属创建的活码',
      shortCode: 'hY8nBmKl',
      type: 'WhatsApp',
      visits: 321,
      pollingCount: getPollingCount('4'),
      status: getShortCodeStatus('4'),
      createdAt: '2024-01-12 14:45:00',
      creatorId: 'subordinate-1',
      creatorName: '王经理',
      description: '下属创建的推广活码'
    },
    // 添加更多测试数据
    ...Array.from({ length: 30 }, (_, i) => ({
      id: `test-${i + 5}`,
      title: `测试活码${i + 1}`,
      shortCode: generateRandomCode(),
      type: ['WhatsApp', 'Telegram', '网址'][i % 3] as 'WhatsApp' | 'Telegram' | '网址',
      visits: Math.floor(Math.random() * 1000),
      pollingCount: Math.floor(Math.random() * 20),
      status: Math.random() > 0.3 ? 'normal' : 'disabled' as 'normal' | 'disabled',
      createdAt: `2024-01-${10 + (i % 20)} 10:30:00`,
      creatorId: i % 2 === 0 ? user.id : 'subordinate-1',
      creatorName: i % 2 === 0 ? user.name : '王经理',
      description: `测试活码${i + 1}的描述`
    }))
  ];

  // 根据视图模式筛选数据
  const getFilteredCodes = () => {
    const allCodes = getAllShortCodes();
    let filteredCodes = [];

    if (viewMode === 'my') {
      filteredCodes = allCodes.filter(code => code.creatorId === user.id);
    } else {
      // 查看下属的活码
      if (user.role === 'super-admin') {
        filteredCodes = allCodes; // 超级管理员可以看到所有
      } else if (user.role === 'boss') {
        filteredCodes = allCodes.filter(code => code.creatorId !== user.id); // Boss看下属的
      } else {
        filteredCodes = []; // 经理没有下属
      }
    }

    // 应用搜索过滤
    if (searchTerm) {
      filteredCodes = filteredCodes.filter(code =>
        code.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        code.shortCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
        code.creatorName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filteredCodes.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  };

  const filteredData = getFilteredCodes();

  // 分页计算
  const totalItems = filteredData.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentPageData = filteredData.slice(startIndex, endIndex);

  // 重置分页当过滤条件改变时
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, viewMode, itemsPerPage]);

  // 分页控制函数
  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  const goToFirstPage = () => goToPage(1);
  const goToLastPage = () => goToPage(totalPages);
  const goToPrevPage = () => goToPage(currentPage - 1);
  const goToNextPage = () => goToPage(currentPage + 1);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'WhatsApp':
        return <MessageCircle className="h-4 w-4 text-green-600" />;
      case 'Telegram':
        return <Send className="h-4 w-4 text-blue-600" />;
      case '网址':
        return <Globe className="h-4 w-4 text-purple-600" />;
      default:
        return <Globe className="h-4 w-4" />;
    }
  };

  const handleCreate = () => {
    if (!newShortCode.title) {
      toast.error('请填写活码标题');
      return;
    }

    // 自动生成目标URL（后端逻辑，前端不显示）
    const targetUrl = getTargetUrl(newShortCode.type);

    toast.success('活码创建成功！');
    console.log('创建活码:', {
      ...newShortCode,
      targetUrl // 包含但不显示的目标URL
    });
    
    // 重置表单
    setNewShortCode({
      title: '',
      type: 'WhatsApp',
      description: '',
      randomCode: ''
    });
    setIsCreateDialogOpen(false);
  };

  const handleEdit = (code: ShortCode) => {
    setEditingCode(code);
    setIsEditDialogOpen(true);
  };

  const handleSaveEdit = () => {
    if (!editingCode || !editingCode.title) {
      toast.error('请填写活码标题');
      return;
    }

    toast.success(`活码 "${editingCode.title}" 信息已更新`);
    console.log('更新活码:', editingCode);
    setIsEditDialogOpen(false);
  };

  const handleDelete = (code: ShortCode) => {
    toast.success(`活码 "${code.title}" 已删除`);
    console.log('删除活码:', code.id);
  };

  // 处理访问量点击事件
  const handleVisitsClick = (shortCodeId: string) => {
    toast.success('跳转到访问记录页面...');
    if (onNavigateToAccess) {
      onNavigateToAccess(shortCodeId);
    }
    console.log('跳转到访问记录:', shortCodeId);
  };

  // 处理轮询数据点击事件
  const handlePollingClick = (shortCodeId: string) => {
    toast.success('跳转到链接轮询页面...');
    if (onNavigateToPolling) {
      onNavigateToPolling(shortCodeId);
    }
    console.log('跳转到链接轮询:', shortCodeId);
  };

  const stats = {
    total: filteredData.length,
    active: filteredData.filter(code => code.status === 'normal').length,
    totalVisits: filteredData.reduce((sum, code) => sum + code.visits, 0),
    avgVisits: filteredData.length > 0 ? Math.round(filteredData.reduce((sum, code) => sum + code.visits, 0) / filteredData.length) : 0
  };

  return (
    <div className="flex-1 space-y-8 p-6 bg-background/95">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl tracking-tight">短码管理</h1>
          <p className="text-muted-foreground text-base">
            管理您的短链接和活码
          </p>
        </div>
        <div className="flex space-x-2">
          {(user.role === 'super-admin' || user.role === 'boss') && (
            <Select value={viewMode} onValueChange={(value: 'my' | 'subordinates') => setViewMode(value)}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="my">我的活码</SelectItem>
                <SelectItem value="subordinates">下属活码</SelectItem>
              </SelectContent>
            </Select>
          )}
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="shadow-sm hover:shadow-md transition-shadow duration-200">
                <Plus className="mr-2 h-4 w-4" />
                创建活码
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[525px]">
              <DialogHeader>
                <DialogTitle>创建新活码</DialogTitle>
                <DialogDescription>
                  创建一个新的短链接活码
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label>活码标题 *</Label>
                  <Input
                    placeholder="输入活码标题"
                    value={newShortCode.title}
                    onChange={(e) => setNewShortCode(prev => ({...prev, title: e.target.value}))}
                  />
                </div>
                <div className="space-y-2">
                  <Label>类型</Label>
                  <Select 
                    value={newShortCode.type} 
                    onValueChange={(value: 'WhatsApp' | 'Telegram' | '网址') => 
                      setNewShortCode(prev => ({...prev, type: value}))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="WhatsApp">
                        <div className="flex items-center space-x-2">
                          <MessageCircle className="h-4 w-4 text-green-600" />
                          <span>WhatsApp</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="Telegram">
                        <div className="flex items-center space-x-2">
                          <Send className="h-4 w-4 text-blue-600" />
                          <span>Telegram</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="网址">
                        <div className="flex items-center space-x-2">
                          <Globe className="h-4 w-4 text-purple-600" />
                          <span>网址</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>随机码</Label>
                  <div className="flex space-x-2">
                    <Input
                      placeholder="随机生成的后缀"
                      value={newShortCode.randomCode}
                      readOnly
                      className="bg-muted"
                    />
                    <Button 
                      variant="outline" 
                      onClick={() => setNewShortCode(prev => ({...prev, randomCode: generateRandomCode()}))}
                    >
                      <RefreshCw className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    完整链接: https://short.ly/{newShortCode.randomCode || '随机生成'}
                  </p>
                </div>
                <div className="space-y-2">
                  <Label>活码描述 (可选)</Label>
                  <Textarea
                    placeholder="输入活码描述"
                    value={newShortCode.description}
                    onChange={(e) => setNewShortCode(prev => ({...prev, description: e.target.value}))}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleCreate}>
                  创建活码
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-0 shadow-md hover:shadow-lg transition-shadow duration-300 bg-gradient-to-br from-white to-blue-50/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-muted-foreground">总活码数</CardTitle>
            <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
              <LinkIcon className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent className="pt-1">
            <div className="text-2xl font-bold tracking-tight mb-1">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              {viewMode === 'my' ? '我创建的活码' : '下属创建的活码'}
            </p>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-md hover:shadow-lg transition-shadow duration-300 bg-gradient-to-br from-white to-emerald-50/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-muted-foreground">正常状态</CardTitle>
            <div className="h-8 w-8 rounded-full bg-emerald-100 flex items-center justify-center">
              <TrendingUp className="h-4 w-4 text-emerald-600" />
            </div>
          </CardHeader>
          <CardContent className="pt-1">
            <div className="text-2xl font-bold tracking-tight mb-1">{stats.active}</div>
            <p className="text-xs text-muted-foreground">
              有轮询数据的活码
            </p>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-md hover:shadow-lg transition-shadow duration-300 bg-gradient-to-br from-white to-indigo-50/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-muted-foreground">总访问量</CardTitle>
            <div className="h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center">
              <Eye className="h-4 w-4 text-indigo-600" />
            </div>
          </CardHeader>
          <CardContent className="pt-1">
            <div className="text-2xl font-bold tracking-tight mb-1">{stats.totalVisits.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              累计访问次数
            </p>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-md hover:shadow-lg transition-shadow duration-300 bg-gradient-to-br from-white to-purple-50/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-muted-foreground">平均访问</CardTitle>
            <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center">
              <UsersIcon className="h-4 w-4 text-purple-600" />
            </div>
          </CardHeader>
          <CardContent className="pt-1">
            <div className="text-2xl font-bold tracking-tight mb-1">{stats.avgVisits}</div>
            <p className="text-xs text-muted-foreground">
              每个活码平均访问
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和过滤 */}
      <Card className="border-0 shadow-md">
        <CardContent className="pt-6">
          <div className="flex space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索活码标题、短码或创建者..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-10 bg-slate-50/50 border-slate-200 focus:bg-white transition-all duration-200"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 活码列表 */}
      <Card className="border-0 shadow-md">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center">
            <div className="h-2 w-2 rounded-full bg-blue-500 mr-2"></div>
            {viewMode === 'my' ? '我的活码' : '下属活码'} (共 {totalItems} 个)
          </CardTitle>
          <CardDescription>
            {viewMode === 'my' ? '您创建的所有活码' : '下属用户创建的活码'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* 分页控件 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Label className="text-sm">每页显示:</Label>
                <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemsPerPage(Number(value))}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
                <span className="text-sm text-muted-foreground">
                  共 {totalItems} 条
                </span>
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToFirstPage}
                  disabled={currentPage === 1}
                >
                  <ChevronsLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPrevPage}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="text-sm text-muted-foreground">
                  第 {currentPage} 页，共 {totalPages} 页
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNextPage}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToLastPage}
                  disabled={currentPage === totalPages}
                >
                  <ChevronsRight className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* 数据表格 */}
            <div className="rounded-lg border border-border/50 overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>标题</TableHead>
                    <TableHead>短码</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>访问量</TableHead>
                    <TableHead>轮询数据</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {currentPageData.map((code, index) => (
                    <TableRow key={code.id} className={`hover:bg-muted/30 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-slate-50/30'}`}>
                      <TableCell className="py-4">
                        <div>
                          <div className="font-medium text-foreground">{code.title}</div>
                          {code.description && (
                            <div className="text-sm text-muted-foreground mt-1">{code.description}</div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <span className="font-mono text-sm bg-slate-100 text-slate-800 px-2.5 py-1.5 rounded-md border">
                          /{code.shortCode}
                        </span>
                      </TableCell>
                      <TableCell className="py-4">
                        <div className="flex items-center space-x-2">
                          {getTypeIcon(code.type)}
                          <Badge variant="outline" className="shadow-sm">{code.type}</Badge>
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <Button
                          variant="ghost"
                          className="h-auto p-1 px-2 font-medium text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors"
                          onClick={() => handleVisitsClick(code.id)}
                        >
                          {code.visits.toLocaleString()}
                        </Button>
                      </TableCell>
                      <TableCell className="py-4">
                        <Button
                          variant="ghost"
                          className="h-auto p-1 px-2 font-medium text-emerald-600 hover:text-emerald-800 hover:bg-emerald-50 rounded-md transition-colors"
                          onClick={() => handlePollingClick(code.id)}
                        >
                          <div className="flex items-center space-x-1">
                            <RotateCcw className="h-3 w-3" />
                            <span>{code.pollingCount}</span>
                          </div>
                        </Button>
                      </TableCell>
                      <TableCell className="py-4">
                        <Badge 
                          variant={code.status === 'normal' ? 'default' : 'secondary'} 
                          className={`shadow-sm ${code.status === 'normal' ? 'bg-emerald-100 text-emerald-800 hover:bg-emerald-200' : ''}`}
                        >
                          {code.status === 'normal' ? '正常' : '未启用'}
                        </Badge>
                      </TableCell>
                      <TableCell className="py-4">
                        <span className="text-sm text-muted-foreground font-mono">{code.createdAt}</span>
                      </TableCell>
                      <TableCell className="py-4">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0 hover:bg-slate-100 transition-colors">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="shadow-lg">
                            {code.creatorId === user.id && (
                              <>
                                <DropdownMenuItem onClick={() => handleEdit(code)} className="hover:bg-blue-50">
                                  <Edit className="mr-2 h-4 w-4" />
                                  编辑
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleDelete(code)}
                                  className="text-destructive hover:bg-red-50"
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  删除
                                </DropdownMenuItem>
                              </>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* 底部分页信息 */}
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <div>
                显示第 {startIndex + 1} 到 {Math.min(endIndex, totalItems)} 条，共 {totalItems} 条数据
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPrevPage}
                  disabled={currentPage === 1}
                >
                  上一页
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNextPage}
                  disabled={currentPage === totalPages}
                >
                  下一页
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 编辑活码对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>编辑活码信息</DialogTitle>
            <DialogDescription>
              只能修改活码标题和描述信息
            </DialogDescription>
          </DialogHeader>
          {editingCode && (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label>活码标题 *</Label>
                <Input
                  value={editingCode.title}
                  onChange={(e) => setEditingCode(prev => prev ? {...prev, title: e.target.value} : null)}
                />
              </div>
              <div className="space-y-2">
                <Label>类型 (不可修改)</Label>
                <div className="flex items-center space-x-2 p-2 bg-muted rounded">
                  {getTypeIcon(editingCode.type)}
                  <span>{editingCode.type}</span>
                </div>
              </div>
              <div className="space-y-2">
                <Label>短码 (不可修改)</Label>
                <div className="font-mono text-sm bg-muted px-2 py-2 rounded">
                  /{editingCode.shortCode}
                </div>
              </div>
              <div className="space-y-2">
                <Label>活码描述</Label>
                <Textarea
                  value={editingCode.description || ''}
                  onChange={(e) => setEditingCode(prev => prev ? {...prev, description: e.target.value} : null)}
                  placeholder="输入活码描述"
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSaveEdit}>
              保存修改
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}