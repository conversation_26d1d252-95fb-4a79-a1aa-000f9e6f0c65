# 企业级活码管理系统 ER 图

## 实体关系图

```mermaid
erDiagram
    USERS {
        bigint id PK
        varchar username
        varchar email
        varchar password_hash
        enum role
        enum status
        bigint created_by FK
        timestamp last_login_at
        timestamp created_at
        timestamp updated_at
    }
    
    DOMAINS {
        bigint id PK
        varchar domain
        date expiry_date
        enum status
        text remark
        timestamp created_at
        timestamp updated_at
    }
    
    SHORT_CODES {
        bigint id PK
        varchar title
        varchar short_code
        enum type
        enum status
        text description
        bigint creator_id FK
        bigint domain_id FK
        bigint visits
        timestamp created_at
        timestamp updated_at
    }
    
    LINK_BATCHES {
        bigint id PK
        varchar name
        bigint short_code_id FK
        int priority
        int priority_count
        int used_count
        enum status
        timestamp created_at
        timestamp updated_at
    }
    
    LINK_DATA {
        bigint id PK
        bigint batch_id FK
        bigint short_code_id FK
        text content
        bigint usage_count
        enum status
        timestamp created_at
        timestamp updated_at
    }
    
    ACCESS_RECORDS {
        bigint id PK
        bigint short_code_id FK
        varchar ip_address
        text user_agent
        enum device_type
        varchar location
        text referer
        text target_content
        timestamp created_at
    }
    
    OPERATION_LOGS {
        bigint id PK
        bigint user_id FK
        varchar user_name
        enum user_role
        varchar action
        varchar target_type
        bigint target_id
        varchar target_name
        text details
        varchar ip_address
        enum status
        timestamp created_at
    }

    USERS ||--o{ USERS : "created_by"
    USERS ||--o{ SHORT_CODES : "creator_id"
    USERS ||--o{ OPERATION_LOGS : "user_id"
    
    DOMAINS ||--o{ SHORT_CODES : "domain_id"
    
    SHORT_CODES ||--o{ LINK_BATCHES : "short_code_id"
    SHORT_CODES ||--o{ LINK_DATA : "short_code_id"
    SHORT_CODES ||--o{ ACCESS_RECORDS : "short_code_id"
    
    LINK_BATCHES ||--o{ LINK_DATA : "batch_id"
```

## 关系说明

### 1. 用户关系 (USERS)
- **自引用关系**: `created_by` 字段引用同表的 `id`，表示用户的创建者
- **层级结构**: 超级管理员 → Boss → 经理

### 2. 短码核心关系 (SHORT_CODES)
- **创建者关系**: 每个短码都有一个创建者 (`creator_id`)
- **域名关系**: 短码可以关联一个域名 (`domain_id`)，可为空
- **一对多关系**: 一个短码可以有多个批次和访问记录

### 3. 链接轮询关系
- **批次管理**: `LINK_BATCHES` 管理轮询批次
- **链接数据**: `LINK_DATA` 存储具体的轮询链接
- **双重关联**: 链接数据同时关联批次和短码

### 4. 访问统计关系 (ACCESS_RECORDS)
- **记录访问**: 每次访问都会产生一条记录
- **统计分析**: 用于生成各种访问统计报表

### 5. 审计日志关系 (OPERATION_LOGS)
- **操作记录**: 记录用户的所有操作行为
- **可选用户**: `user_id` 可为空（系统操作）

## 索引策略

### 主要查询索引
1. **用户查询**: `email`, `username`, `role`, `status`
2. **短码查询**: `short_code`, `creator_id`, `type`, `status`
3. **访问记录**: `short_code_id + created_at` 组合索引
4. **操作日志**: `user_id + created_at` 组合索引

### 复合索引
- `short_codes(creator_id, type)`: 按创建者和类型查询
- `access_records(short_code_id, created_at)`: 时间范围统计
- `link_data(batch_id, status)`: 批次状态查询

## 数据完整性

### 外键约束
- 启用外键约束确保数据一致性
- 级联删除：删除短码时同时删除相关批次和访问记录
- 设置NULL：删除域名时不影响短码，仅置空关联

### 业务规则
1. **短码唯一性**: `short_code` 字段全局唯一
2. **邮箱唯一性**: `email` 字段全局唯一
3. **域名唯一性**: `domain` 字段全局唯一
4. **状态枚举**: 使用枚举类型确保状态值有效性

## 性能优化

### 分区策略
- `access_records` 表可按时间分区
- `operation_logs` 表可按月份分区

### 存储优化
- 使用 InnoDB 引擎支持事务
- 设置合适的字符集 utf8mb4
- 定期清理历史数据

### 查询优化
- 为常用查询字段建立索引
- 避免全表扫描
- 使用分页查询减少数据传输