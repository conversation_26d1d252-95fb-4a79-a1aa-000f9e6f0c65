import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { But<PERSON> } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from './ui/dropdown-menu';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from './ui/alert-dialog';
import { 
  Plus, 
  Search, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Globe,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Filter,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react';
import { toast } from 'sonner@2.0.3';

interface User {
  id: string;
  name: string;
  role: 'super-admin' | 'boss' | 'manager';
}

interface Domain {
  id: string;
  domain: string;
  expiryDate: string;
  status: 'active' | 'expired';
  addedDate: string;
  remark?: string;
  linkedCodes: number;
}

interface DomainManagementProps {
  user: User;
}

export function DomainManagement({ user }: DomainManagementProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingDomain, setEditingDomain] = useState<Domain | null>(null);
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // 模拟数据 - 增加更多测试数据
  const domains: Domain[] = [
    {
      id: '1',
      domain: 'abc.com',
      expiryDate: '2024-12-30',
      status: 'active',
      addedDate: '2024-01-01 10:00:00',
      remark: '主要推广域名',
      linkedCodes: 25
    },
    {
      id: '2',
      domain: 'xyz.com',
      expiryDate: '2024-11-15',
      status: 'active',
      addedDate: '2024-01-05 14:30:00',
      remark: '备用域名',
      linkedCodes: 12
    },
    {
      id: '3',
      domain: 'def.com',
      expiryDate: '2025-03-20',
      status: 'active',
      addedDate: '2024-01-10 09:15:00',
      remark: '新注册域名',
      linkedCodes: 8
    },
    {
      id: '4',
      domain: 'old.com',
      expiryDate: '2024-01-10',
      status: 'expired',
      addedDate: '2023-12-01 16:45:00',
      remark: '已过期域名',
      linkedCodes: 0
    },
    {
      id: '5',
      domain: 'test.com',
      expiryDate: '2024-01-20',
      status: 'expired',
      addedDate: '2023-11-15 11:20:00',
      remark: '测试域名',
      linkedCodes: 3
    },
    // 添加更多测试数据
    ...Array.from({ length: 20 }, (_, i) => ({
      id: `test-${i + 6}`,
      domain: `domain${i + 1}.com`,
      expiryDate: `2024-${Math.floor(Math.random() * 12) + 1}-${Math.floor(Math.random() * 28) + 1}`,
      status: Math.random() > 0.8 ? 'expired' : 'active' as 'active' | 'expired',
      addedDate: `2024-01-${10 + (i % 20)} 10:30:00`,
      remark: [`推广域名${i + 1}`, '备用域名', '测试域名', ''][Math.floor(Math.random() * 4)],
      linkedCodes: Math.floor(Math.random() * 50)
    }))
  ];

  const [formData, setFormData] = useState({
    domain: '',
    expiryDate: '',
    remark: ''
  });

  const isExpiringSoon = (expiryDate: string) => {
    const expiry = new Date(expiryDate);
    const today = new Date();
    const daysUntilExpiry = Math.ceil((expiry.getTime() - today.getTime()) / (1000 * 3600 * 24));
    return daysUntilExpiry <= 5 && daysUntilExpiry > 0;
  };

  const getFilteredDomains = () => {
    let filtered = domains;
    
    const matchesSearch = searchTerm === '' || filtered.filter(domain => {
      return domain.domain.toLowerCase().includes(searchTerm.toLowerCase()) ||
             (domain.remark && domain.remark.toLowerCase().includes(searchTerm.toLowerCase()));
    });
    
    if (searchTerm) {
      filtered = matchesSearch;
    }
    
    const matchesStatus = selectedStatus === 'all' || filtered.filter(domain => domain.status === selectedStatus);
    
    if (selectedStatus !== 'all') {
      filtered = matchesStatus;
    }
    
    return filtered.sort((a, b) => new Date(b.addedDate).getTime() - new Date(a.addedDate).getTime());
  };

  const filteredDomains = getFilteredDomains();

  // 分页计算
  const totalItems = filteredDomains.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentPageData = filteredDomains.slice(startIndex, endIndex);

  // 重置分页当过滤条件改变时
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, selectedStatus, itemsPerPage]);

  // 分页控制函数
  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  const goToFirstPage = () => goToPage(1);
  const goToLastPage = () => goToPage(totalPages);
  const goToPrevPage = () => goToPage(currentPage - 1);
  const goToNextPage = () => goToPage(currentPage + 1);

  const handleCreateSubmit = () => {
    if (!formData.domain || !formData.expiryDate) {
      toast.error('请填写域名地址和有效期');
      return;
    }
    
    toast.success(`域名 "${formData.domain}" 添加成功`);
    console.log('添加域名:', formData);
    setIsCreateDialogOpen(false);
    setFormData({ domain: '', expiryDate: '', remark: '' });
  };

  const handleEdit = (domain: Domain) => {
    setEditingDomain(domain);
    setFormData({
      domain: domain.domain,
      expiryDate: domain.expiryDate,
      remark: domain.remark || ''
    });
    setIsEditDialogOpen(true);
  };

  const handleEditSubmit = () => {
    if (!editingDomain) return;
    
    toast.success(`域名 "${editingDomain.domain}" 信息已更新`);
    console.log('编辑域名:', editingDomain?.id, formData);
    setIsEditDialogOpen(false);
    setEditingDomain(null);
  };

  const handleDelete = (domain: Domain) => {
    toast.success(`域名 "${domain.domain}" 已删除`);
    console.log('删除域名:', domain.id);
  };

  // 统计数据
  const stats = {
    total: domains.length,
    active: domains.filter(d => d.status === 'active').length,
    expired: domains.filter(d => d.status === 'expired').length,
    expiringSoon: domains.filter(d => d.status === 'active' && isExpiringSoon(d.expiryDate)).length
  };

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl">域名管理</h1>
          <p className="text-muted-foreground">
            管理系统域名和有效期监控
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              添加域名
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[525px]">
            <DialogHeader>
              <DialogTitle>添加域名</DialogTitle>
              <DialogDescription>
                添加新的前端域名到系统中
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="domain">域名地址 *</Label>
                <Input
                  id="domain"
                  placeholder="例如：example.com"
                  value={formData.domain}
                  onChange={(e) => setFormData({...formData, domain: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="expiryDate">有效期 *</Label>
                <Input
                  id="expiryDate"
                  type="date"
                  value={formData.expiryDate}
                  onChange={(e) => setFormData({...formData, expiryDate: e.target.value})}
                />
                <p className="text-xs text-muted-foreground">
                  选择域名的到期日期
                </p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="remark">备注</Label>
                <Textarea
                  id="remark"
                  placeholder="选填，域名用途说明"
                  maxLength={100}
                  value={formData.remark}
                  onChange={(e) => setFormData({...formData, remark: e.target.value})}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleCreateSubmit}>
                确定
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">域名总数</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              系统管理的域名数量
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">在用域名</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.active}</div>
            <p className="text-xs text-muted-foreground">
              有效期内可用域名
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">即将过期</CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.expiringSoon}</div>
            <p className="text-xs text-muted-foreground">
              5天内到期域名
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已过期</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.expired}</div>
            <p className="text-xs text-muted-foreground">
              需要续费或更换
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索域名或备注..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-40">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="active">在用</SelectItem>
                <SelectItem value="expired">过期</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 域名列表 */}
      <Card>
        <CardHeader>
          <CardTitle>域名列表 (共 {totalItems} 个域名)</CardTitle>
          <CardDescription>
            系统域名配置和状态管理
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* 分页控件 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Label className="text-sm">每页显示:</Label>
                <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemsPerPage(Number(value))}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
                <span className="text-sm text-muted-foreground">
                  共 {totalItems} 条
                </span>
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToFirstPage}
                  disabled={currentPage === 1}
                >
                  <ChevronsLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPrevPage}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="text-sm text-muted-foreground">
                  第 {currentPage} 页，共 {totalPages} 页
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNextPage}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToLastPage}
                  disabled={currentPage === totalPages}
                >
                  <ChevronsRight className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* 数据表格 */}
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>编号</TableHead>
                  <TableHead>域名地址</TableHead>
                  <TableHead>有效期</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>关联活码</TableHead>
                  <TableHead>添加时间</TableHead>
                  <TableHead>备注</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentPageData.map((domain, index) => {
                  const expiringSoon = isExpiringSoon(domain.expiryDate);
                  
                  return (
                    <TableRow key={domain.id}>
                      <TableCell>{startIndex + index + 1}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Globe className="h-4 w-4 text-muted-foreground" />
                          <span className="font-mono">{domain.domain}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-3 w-3 text-muted-foreground" />
                          <span className={expiringSoon ? 'text-yellow-600' : ''}>
                            {domain.expiryDate}
                          </span>
                          {expiringSoon && (
                            <AlertTriangle className="h-3 w-3 text-yellow-600" />
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={domain.status === 'active' ? 'default' : 'destructive'}>
                          {domain.status === 'active' ? '在用' : '过期'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {domain.linkedCodes} 个活码
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm">{domain.addedDate}</span>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm text-muted-foreground">
                          {domain.remark || '-'}
                        </span>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEdit(domain)}>
                              <Edit className="mr-2 h-4 w-4" />
                              编辑域名
                            </DropdownMenuItem>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <DropdownMenuItem 
                                  className="text-destructive"
                                  onSelect={(e) => e.preventDefault()}
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  删除域名
                                </DropdownMenuItem>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>确认删除域名</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    您确定要删除域名 "{domain.domain}" 吗？
                                    {domain.linkedCodes > 0 && (
                                      <span className="text-destructive">
                                        <br />警告：该域名关联了 {domain.linkedCodes} 个活码，删除后这些活码将无法正常使用。
                                      </span>
                                    )}
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>取消</AlertDialogCancel>
                                  <AlertDialogAction 
                                    onClick={() => handleDelete(domain)}
                                    className="bg-destructive text-destructive-foreground"
                                  >
                                    确认删除
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>

            {/* 底部分页信息 */}
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <div>
                显示第 {startIndex + 1} 到 {Math.min(endIndex, totalItems)} 条，共 {totalItems} 条数据
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPrevPage}
                  disabled={currentPage === 1}
                >
                  上一页
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNextPage}
                  disabled={currentPage === totalPages}
                >
                  下一页
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 编辑域名对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[525px]">
          <DialogHeader>
            <DialogTitle>编辑域名</DialogTitle>
            <DialogDescription>
              修改域名信息和有效期
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-domain">域名地址</Label>
              <Input
                id="edit-domain"
                value={formData.domain}
                readOnly
                className="bg-muted"
              />
              <p className="text-xs text-muted-foreground">域名地址创建后不可修改</p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-expiryDate">有效期 *</Label>
              <Input
                id="edit-expiryDate"
                type="date"
                value={formData.expiryDate}
                onChange={(e) => setFormData({...formData, expiryDate: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-remark">备注</Label>
              <Textarea
                id="edit-remark"
                maxLength={100}
                value={formData.remark}
                onChange={(e) => setFormData({...formData, remark: e.target.value})}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleEditSubmit}>
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}