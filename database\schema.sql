-- 企业级活码管理系统数据库架构
-- 数据库: shortcode_management
-- 字符集: utf8mb4
-- 排序规则: utf8mb4_unicode_ci

-- 用户表
CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱地址',
  `password_hash` varchar(255) NOT NULL COMMENT '密码哈希',
  `role` enum('super-admin','boss','manager') NOT NULL DEFAULT 'manager' COMMENT '用户角色',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '账户状态',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '创建者ID',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`),
  KEY `fk_created_by` (`created_by`),
  CONSTRAINT `fk_users_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 域名管理表
CREATE TABLE `domains` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `domain` varchar(255) NOT NULL COMMENT '域名地址',
  `expiry_date` date NOT NULL COMMENT '到期日期',
  `status` enum('active','expired') NOT NULL DEFAULT 'active' COMMENT '域名状态',
  `remark` text COMMENT '备注信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_domain` (`domain`),
  KEY `idx_status` (`status`),
  KEY `idx_expiry_date` (`expiry_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='域名管理表';

-- 短码表
CREATE TABLE `short_codes` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL COMMENT '活码标题',
  `short_code` varchar(50) NOT NULL COMMENT '短码',
  `type` enum('WhatsApp','Telegram','网址') NOT NULL COMMENT '类型',
  `status` enum('normal','disabled') NOT NULL DEFAULT 'disabled' COMMENT '状态',
  `description` text COMMENT '描述',
  `creator_id` bigint(20) unsigned NOT NULL COMMENT '创建者ID',
  `domain_id` bigint(20) unsigned DEFAULT NULL COMMENT '关联域名ID',
  `visits` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '访问次数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_short_code` (`short_code`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `fk_domain_id` (`domain_id`),
  CONSTRAINT `fk_short_codes_creator` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_short_codes_domain` FOREIGN KEY (`domain_id`) REFERENCES `domains` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短码表';

-- 链接批次表
CREATE TABLE `link_batches` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(200) NOT NULL COMMENT '批次名称',
  `short_code_id` bigint(20) unsigned NOT NULL COMMENT '关联短码ID',
  `priority` int(11) NOT NULL DEFAULT 1 COMMENT '优先级',
  `priority_count` int(11) NOT NULL DEFAULT 5 COMMENT '优先轮询次数',
  `used_count` int(11) NOT NULL DEFAULT 0 COMMENT '已使用次数',
  `status` enum('priority','normal','disabled') NOT NULL DEFAULT 'priority' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_short_code_id` (`short_code_id`),
  KEY `idx_priority` (`priority`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_batches_short_code` FOREIGN KEY (`short_code_id`) REFERENCES `short_codes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='链接批次表';

-- 链接数据表
CREATE TABLE `link_data` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `batch_id` bigint(20) unsigned NOT NULL COMMENT '批次ID',
  `short_code_id` bigint(20) unsigned NOT NULL COMMENT '短码ID',
  `content` text NOT NULL COMMENT '链接内容',
  `usage_count` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '使用次数',
  `status` enum('active','disabled') NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_short_code_id` (`short_code_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_link_data_batch` FOREIGN KEY (`batch_id`) REFERENCES `link_batches` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_link_data_short_code` FOREIGN KEY (`short_code_id`) REFERENCES `short_codes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='链接数据表';

-- 访问记录表
CREATE TABLE `access_records` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `short_code_id` bigint(20) unsigned NOT NULL COMMENT '短码ID',
  `ip_address` varchar(45) NOT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `device_type` enum('desktop','mobile','tablet') NOT NULL COMMENT '设备类型',
  `location` varchar(200) COMMENT '地理位置',
  `referer` text COMMENT '来源页面',
  `target_content` text COMMENT '跳转目标',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_short_code_id` (`short_code_id`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_device_type` (`device_type`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_access_records_short_code` FOREIGN KEY (`short_code_id`) REFERENCES `short_codes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='访问记录表';

-- 操作日志表
CREATE TABLE `operation_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned DEFAULT NULL COMMENT '操作用户ID',
  `user_name` varchar(50) COMMENT '用户名',
  `user_role` enum('super-admin','boss','manager','system') NOT NULL COMMENT '用户角色',
  `action` varchar(100) NOT NULL COMMENT '操作动作',
  `target_type` varchar(50) NOT NULL COMMENT '目标类型',
  `target_id` bigint(20) unsigned DEFAULT NULL COMMENT '目标ID',
  `target_name` varchar(200) COMMENT '目标名称',
  `details` text COMMENT '操作详情',
  `ip_address` varchar(45) COMMENT 'IP地址',
  `status` enum('success','failed','warning') NOT NULL DEFAULT 'success' COMMENT '操作状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_target_type` (`target_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_operation_logs_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- 创建索引以优化查询性能
CREATE INDEX idx_short_codes_creator_type ON short_codes(creator_id, type);
CREATE INDEX idx_access_records_time_range ON access_records(short_code_id, created_at);
CREATE INDEX idx_link_data_batch_status ON link_data(batch_id, status);
CREATE INDEX idx_operation_logs_user_time ON operation_logs(user_id, created_at);

-- 初始化超级管理员用户
INSERT INTO `users` (`username`, `email`, `password_hash`, `role`, `status`) 
VALUES ('admin', '<EMAIL>', '$2y$10$hash_value_here', 'super-admin', 'active');