# 活码管理系统 (Live Code Management System)

一个完整的企业级短链接管理平台，支持多类型链接轮询、用户权限管理和访问统计。

## 功能特性

- **多级权限系统**: 支持超级管理员、Boss、经理三级权限
- **短码管理**: 创建和管理 WhatsApp、Telegram、网址类型的短链接
- **链接轮询**: 批次化管理链接数据，支持优先轮询机制
- **访问统计**: 详细的访问记录和统计分析
- **用户管理**: 完整的用户创建、编辑、权限控制功能
- **操作审计**: 完整的操作日志记录和审计功能

## 技术栈

- **前端**: React + TypeScript + Tailwind CSS v4
- **UI组件**: Shadcn UI + Radix UI
- **通知系统**: Sonner Toast
- **图标**: Lucide React
- **数据库**: MySQL 8.0+ (设计文档在 `/database` 目录)

## 项目结构

```
├── App.tsx                 # 主应用入口
├── components/             # React 组件
│   ├── ui/                # Shadcn UI 组件
│   ├── Dashboard.tsx      # 工作台
│   ├── ShortCodeManagement.tsx  # 短码管理
│   ├── AccessRecords.tsx  # 访问记录
│   ├── UserManagement.tsx # 用户管理
│   ├── LinkPolling.tsx    # 链接轮询
│   └── OperationLogs.tsx  # 操作日志
├── types/                 # TypeScript 类型定义
├── constants/             # 常量和辅助函数
├── database/              # 数据库设计和 ER 图
└── styles/                # 全局样式
```

## 快速开始

### 环境要求

- Node.js 18+
- MySQL 8.0+
- 现代浏览器支持

### 安装依赖

```bash
# 安装 Sonner (Toast 通知)
npm install sonner@2.0.3

# 其他依赖已通过 import 语法自动安装
```

### 数据库设置

```bash
# 1. 创建数据库
mysql -u root -p < database/schema.sql

# 2. 创建应用用户
mysql -u root -p -e "
CREATE USER 'app_user'@'%' IDENTIFIED BY 'your_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON live_code_system.* TO 'app_user'@'%';
FLUSH PRIVILEGES;
"
```

### 默认登录账户

- **超级管理员**: <EMAIL> / password
- **Boss**: <EMAIL> / password  
- **经理**: <EMAIL> / password

## 在 Cursor 中使用 Claude 3.5 Sonnet

### 配置说明

在 Cursor 编辑器中使用 Claude 3.5 Sonnet AI 助手时，请按以下方式配置：

#### 1. 模型选择
- 打开 Cursor 设置 (Ctrl/Cmd + ,)
- 选择 **Models** 标签
- 在 **Chat Model** 中选择 `claude-3-5-sonnet-20241022`
- 在 **Autocomplete Model** 中选择 `claude-3-5-sonnet-20241022`

#### 2. 项目上下文配置
在 Cursor 中创建 `.cursorrules` 文件：

```
# 活码管理系统开发规则

## 项目概述
这是一个企业级活码(短链接)管理系统，使用 React + TypeScript + Tailwind CSS v4 开发。

## 开发规则
1. 严格遵循现有的类型定义 (types/index.ts)
2. 使用 constants/ 目录中的常量和辅助函数
3. 保持组件文件大小合理，必要时提取子组件
4. 使用 Shadcn UI 组件，避免创建自定义样式
5. 所有用户交互都要有 Toast 通知反馈
6. 遵循三级权限系统的数据访问控制

## 样式规则
- 使用 Tailwind CSS v4 语法
- 不要覆盖 globals.css 中的字体设置
- 使用现有的 CSS 变量和主题系统
- 保持响应式设计

## 数据模拟
- 使用 constants/ 中的 MOCK_* 数据
- 权限控制基于 user.role 属性
- 数据过滤遵循业务逻辑规则
```

#### 3. 智能提示配置
在项目根目录创建 `.vscode/settings.json`：

```json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.autoImports": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  }
}
```

#### 4. 与 Claude 对话的最佳实践

**有效的提示示例**:
```
请帮我优化 ShortCodeManagement 组件的搜索功能，需要：
1. 支持按标题和短码搜索
2. 保持现有的权限控制逻辑  
3. 使用已有的类型定义和常量
```

**避免的提示**:
```
帮我重写整个组件 (过于宽泛)
创建新的 UI 库 (违反项目约定)
```

### 调试技巧

1. **类型错误**: 检查 `types/index.ts` 中的类型定义
2. **样式问题**: 参考 `styles/globals.css` 中的 CSS 变量
3. **权限问题**: 检查用户角色和权限过滤逻辑
4. **数据问题**: 使用 `constants/` 中的模拟数据

### 版本要求

- **Cursor**: 最新版本
- **Claude Model**: 3.5 Sonnet (20241022)
- **TypeScript**: 5.0+
- **React**: 18+

## 权限说明

### 数据可见性规则
- **超级管理员**: 查看所有数据
- **Boss**: 查看自己和下属的数据  
- **经理**: 只查看自己的数据

### 功能权限
- **域名管理**: 仅超级管理员
- **用户管理**: 超级管理员和 Boss
- **操作日志**: 超级管理员和 Boss
- **其他功能**: 所有角色

## 数据库设计

详细的数据库设计文档和 ER 图请查看 `/database` 目录：

- `schema.sql`: 完整建表语句
- `er_diagram.md`: 实体关系图
- `README.md`: 数据库设计说明

## 贡献指南

1. 遵循现有的代码结构和命名规范
2. 新功能需要添加对应的类型定义
3. 组件过大时及时重构和拆分
4. 保持 UI 一致性，使用 Shadcn UI 组件
5. 添加适当的错误处理和用户反馈

## 许可证

MIT License