import { OperationLog, User } from '../types';
import { MOCK_OPERATION_LOGS } from './index';

export interface LogStats {
  total: number;
  today: number;
  success: number;
  failed: number;
  warning: number;
}

// 生成详细的操作详情
export const generateOperationDetails = (action: string, targetType: string, targetName: string, userRole: string): string => {
  switch (action) {
    case '创建活码':
      return `创建了新的${targetType === 'short_code' ? '短链接活码' : '活码'}"${targetName}"，类型：${getTargetTypeFromName(targetName)}`;
    
    case '编辑活码':
      return `修改了活码"${targetName}"的配置信息，包括标题、描述或目标链接`;
    
    case '删除活码':
      return `永久删除了活码"${targetName}"及其所有关联数据`;
    
    case '批量添加':
      return `为活码"${targetName}"批量添加了链接数据，新增${Math.floor(Math.random() * 20) + 5}个有效链接`;
    
    case '删除用户':
      return `删除了用户账户"${targetName}"，同时清理了该用户的所有相关权限和数据`;
    
    case '创建用户':
      return `创建了新用户账户"${targetName}"，初始角色：${getRoleDisplayName(targetType)}`;
    
    case '重置密码':
      return `重置了用户"${targetName}"的登录密码，用户下次登录时需要修改密码`;
    
    case '修改权限':
      return `修改了用户"${targetName}"的系统权限，角色变更为：${getRoleDisplayName(targetType)}`;
    
    case '登录失败':
      return `尝试登录系统失败，原因：${getFailureReason()}，IP地址：${generateRandomIP()}`;
    
    case '登录成功':
      return `成功登录系统，使用${getLoginMethod()}方式验证身份`;
    
    case '域名过期':
      return `域名"${targetName}"即将在${Math.floor(Math.random() * 7) + 1}天内过期，请及时续费`;
    
    case '域名添加':
      return `添加了新域名"${targetName}"到系统中，状态：待验证`;
    
    case '域名删除':
      return `从系统中移除了域名"${targetName}"，相关活码已停止使用该域名`;
    
    case '系统维护':
      return `执行了系统维护操作：${getMaintenanceType()}，持续时间约${Math.floor(Math.random() * 60) + 10}分钟`;
    
    case '数据导出':
      return `导出了${targetType}数据，文件名：${targetName}_${new Date().getTime()}.xlsx`;
    
    case '访问记录':
      return `查看了活码"${targetName}"的访问记录和统计数据`;
    
    case '轮询配置':
      return `修改了活码"${targetName}"的链接轮询配置，优先级设置为${Math.floor(Math.random() * 10) + 1}`;
    
    default:
      return `执行了${action}操作，目标：${targetName}`;
  }
};

// 根据活码名称推断类型
const getTargetTypeFromName = (name: string): string => {
  if (name.includes('WS') || name.includes('WhatsApp')) return 'WhatsApp';
  if (name.includes('TG') || name.includes('Telegram')) return 'Telegram';
  if (name.includes('官网') || name.includes('网站')) return '网址';
  return '未知类型';
};

// 获取角色显示名称
const getRoleDisplayName = (role: string): string => {
  const roleMap: { [key: string]: string } = {
    'super-admin': '超级管理员',
    'boss': 'Boss',
    'manager': '经理'
  };
  return roleMap[role] || role;
};

// 生成登录失败原因
const getFailureReason = (): string => {
  const reasons = ['密码错误', '账户已锁定', '验证码错误', '账户不存在', '权限不足'];
  return reasons[Math.floor(Math.random() * reasons.length)];
};

// 生成登录方式
const getLoginMethod = (): string => {
  const methods = ['用户名密码', '短信验证码', '邮箱验证', '二维码扫描'];
  return methods[Math.floor(Math.random() * methods.length)];
};

// 生成维护类型
const getMaintenanceType = (): string => {
  const types = ['数据库优化', '缓存清理', '日志归档', '系统更新', '安全补丁'];
  return types[Math.floor(Math.random() * types.length)];
};

// 生成随机IP地址
const generateRandomIP = (): string => {
  return `${Math.floor(Math.random() * 255) + 1}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;
};

// 生成目标名称
export const generateTargetName = (action: string, targetType: string): string => {
  switch (targetType) {
    case 'short_code':
      const codeNames = ['WS客服主链接', 'TG推广渠道A', '官网导流页面', '活动专页', '客服咨询链接'];
      return codeNames[Math.floor(Math.random() * codeNames.length)];
    
    case 'user':
      if (action === '删除用户') {
        const deletedUsers = ['离职员工', '测试账户', '临时用户'];
        return deletedUsers[Math.floor(Math.random() * deletedUsers.length)];
      }
      const userNames = ['张经理', '李主管', '王助理', '刘专员', '陈总监'];
      return userNames[Math.floor(Math.random() * userNames.length)];
    
    case 'domain':
      const domains = ['short.ly', 'quick.ly', 'link.ly', 'go.ly', 'jump.ly'];
      return domains[Math.floor(Math.random() * domains.length)];
    
    case 'link_data':
      const batchNames = ['新年推广批次', '春节活动组', '客服账号集', '渠道推广A组', '备用链接池'];
      return batchNames[Math.floor(Math.random() * batchNames.length)];
    
    case 'auth':
      return '登录系统';
    
    case 'system':
      return '系统维护';
    
    default:
      return `未知目标-${Math.random().toString(36).substr(2, 8)}`;
  }
};

export const filterLogsByPermission = (logs: OperationLog[], currentUser: User): OperationLog[] => {
  if (currentUser.role === 'manager') {
    // 经理只能看自己的操作日志
    return logs.filter(log => log.userId === currentUser.id);
  }
  // super-admin 和 boss 可以看到所有日志
  return logs;
};

export const filterLogsBySearch = (logs: OperationLog[], searchTerm: string): OperationLog[] => {
  if (!searchTerm) return logs;
  
  return logs.filter(log =>
    log.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
    log.targetName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    log.details.toLowerCase().includes(searchTerm.toLowerCase()) ||
    log.ipAddress.toLowerCase().includes(searchTerm.toLowerCase())
  );
};

export const filterLogsByAction = (logs: OperationLog[], actionFilter: string): OperationLog[] => {
  if (actionFilter === 'all') return logs;
  return logs.filter(log => log.action === actionFilter);
};

export const filterLogsByUser = (logs: OperationLog[], userFilter: string): OperationLog[] => {
  if (userFilter === 'all') return logs;
  return logs.filter(log => log.userRole === userFilter);
};

export const filterLogsByTime = (logs: OperationLog[], timeFilter: string): OperationLog[] => {
  if (timeFilter === 'all') return logs;
  
  const now = new Date();
  const filterDate = new Date();
  
  switch (timeFilter) {
    case 'today':
      filterDate.setHours(0, 0, 0, 0);
      break;
    case 'week':
      filterDate.setDate(now.getDate() - 7);
      break;
    case 'month':
      filterDate.setMonth(now.getMonth() - 1);
      break;
    default:
      return logs;
  }
  
  return logs.filter(log => new Date(log.createdAt) >= filterDate);
};

export const sortLogsByDate = (logs: OperationLog[]): OperationLog[] => {
  return logs.sort((a, b) => 
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );
};

export const calculateLogStats = (logs: OperationLog[]): LogStats => {
  const today = new Date();
  
  return {
    total: logs.length,
    today: logs.filter(log => {
      const logDate = new Date(log.createdAt);
      return logDate.toDateString() === today.toDateString();
    }).length,
    success: logs.filter(log => log.status === 'success').length,
    failed: logs.filter(log => log.status === 'failed').length,
    warning: logs.filter(log => log.status === 'warning').length
  };
};

export const getUniqueActions = (logs: OperationLog[]): string[] => {
  return Array.from(new Set(logs.map(log => log.action)));
};

export const getMockOperationLogs = (currentUser: User): OperationLog[] => {
  // 生成更多详细的mock数据
  const enhancedLogs = MOCK_OPERATION_LOGS.map(log => ({
    ...log,
    userId: log.userId === 'current-user' ? currentUser.id : log.userId,
    userName: log.userName === '当前用户' ? currentUser.name : log.userName,
    userRole: log.userRole === 'manager' && log.userId === 'current-user' ? currentUser.role : log.userRole,
    details: generateOperationDetails(log.action, log.targetType, log.targetName, log.userRole),
    ipAddress: log.ipAddress === 'system' ? 'system' : generateRandomIP()
  }));

  // 添加更多模拟数据
  const additionalLogs: OperationLog[] = Array.from({ length: 20 }, (_, i) => {
    const actions = ['创建活码', '编辑活码', '删除活码', '批量添加', '创建用户', '修改权限', '登录成功', '访问记录', '轮询配置', '数据导出'];
    const targetTypes = ['short_code', 'user', 'link_data', 'domain', 'auth', 'system'];
    const userRoles = ['super-admin', 'boss', 'manager'];
    const statuses: ('success' | 'failed' | 'warning')[] = ['success', 'success', 'success', 'failed', 'warning'];
    
    const action = actions[Math.floor(Math.random() * actions.length)];
    const targetType = targetTypes[Math.floor(Math.random() * targetTypes.length)];
    const targetName = generateTargetName(action, targetType);
    const userRole = userRoles[Math.floor(Math.random() * userRoles.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    
    const date = new Date();
    date.setDate(date.getDate() - Math.floor(Math.random() * 30));
    date.setHours(Math.floor(Math.random() * 24));
    date.setMinutes(Math.floor(Math.random() * 60));
    
    return {
      id: `generated-${i + 100}`,
      userId: userRole === currentUser.role ? currentUser.id : `user-${i + 100}`,
      userName: userRole === currentUser.role ? currentUser.name : ['张总', '李经理', '王主管', '刘专员'][Math.floor(Math.random() * 4)],
      userRole,
      action,
      targetType,
      targetName,
      details: generateOperationDetails(action, targetType, targetName, userRole),
      ipAddress: generateRandomIP(),
      createdAt: date.toISOString().replace('T', ' ').substr(0, 19),
      status
    };
  });

  return [...enhancedLogs, ...additionalLogs];
};